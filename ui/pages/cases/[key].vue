<template>
  <div class="case-detail-page min-h-screen bg-gray-900">
    <!-- 加载状态（包括失败）- 显示骨架图 -->
    <div v-if="!isDataReady" class="container mx-auto px-2 md:px-4 py-4 md:py-6 relative">
      <!-- 箱子详情骨架 - 匹配星空背景效果 -->
      <div class="relative bg-gray-900/95 backdrop-blur-md rounded-2xl border border-gray-700/50 mb-6 overflow-hidden">
        <!-- 星空背景效果 -->
        <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-blue-900/20 to-purple-900/20"></div>
        
        <!-- 星点装饰 -->
        <div class="absolute top-8 left-12 w-1 h-1 bg-white/40 rounded-full animate-pulse"></div>
        <div class="absolute top-16 right-20 w-0.5 h-0.5 bg-blue-300/60 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
        <div class="absolute top-24 left-1/3 w-1.5 h-1.5 bg-purple-300/50 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
        <div class="absolute bottom-20 right-16 w-1 h-1 bg-cyan-300/40 rounded-full animate-pulse" style="animation-delay: 1.5s;"></div>
        <div class="absolute bottom-32 left-20 w-0.5 h-0.5 bg-white/30 rounded-full animate-pulse" style="animation-delay: 2s;"></div>
        <div class="absolute top-1/3 right-1/4 w-1 h-1 bg-blue-200/50 rounded-full animate-pulse" style="animation-delay: 2.5s;"></div>
        
        <!-- 浮动光球装饰 -->
        <div class="absolute top-6 right-8 w-24 h-24 bg-blue-500/8 rounded-full blur-2xl animate-pulse"></div>
        <div class="absolute bottom-8 left-12 w-32 h-32 bg-purple-500/6 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
        <div class="absolute top-1/2 right-1/3 w-16 h-16 bg-cyan-500/10 rounded-full blur-xl animate-pulse" style="animation-delay: 2s;"></div>
        
        <div class="relative p-6 lg:p-8">
          <div class="flex flex-col lg:flex-row items-center lg:items-start gap-8">
            <!-- 箱子图片骨架 - 匹配增强后的发光效果 -->
            <div class="relative group">
              <div class="w-48 h-48 lg:w-64 lg:h-64 relative">
                <!-- 简化的底层发光效果 - 仅保留核心光晕 -->
                <div class="absolute -inset-2 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl blur-xl opacity-60 animate-pulse"></div>
                
                <!-- 箱子图片骨架容器 -->
                <div class="relative w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-800/60 to-gray-900/80 rounded-xl transition-all duration-300 backdrop-blur-sm overflow-hidden">
                  <!-- 箱子图片骨架占位 -->
                  <div class="relative z-10 w-4/5 h-4/5 bg-gray-600/50 rounded-lg animate-pulse flex items-center justify-center">
                    <!-- 箱子图标占位 -->
                    <div class="w-16 h-16 bg-gray-500/30 rounded-lg flex items-center justify-center">
                      <div class="text-gray-400/50 text-2xl">📦</div>
                    </div>
                  </div>
                  
                  <!-- 微妙的光泽效果 -->
                  <div class="absolute top-4 left-4 w-16 h-16 bg-gradient-to-br from-white/10 to-transparent rounded-full opacity-50 animate-pulse blur-sm"></div>
                </div>
              </div>
            </div>
          
            <!-- 箱子信息骨架 -->
            <div class="flex-1 text-center lg:text-left">
              <UiCSGOSkeleton type="line" height="2.5rem" width="60%" />
              <div class="mt-4 mb-6 flex flex-wrap gap-3 justify-center lg:justify-start">
                <UiCSGOSkeleton type="button" height="2rem" width="5rem" />
                <UiCSGOSkeleton type="button" height="2rem" width="4rem" />
                <UiCSGOSkeleton type="button" height="2rem" width="6rem" />
              </div>
              <UiCSGOSkeleton type="line" height="1rem" width="80%" />
              <div class="mt-2 mb-6">
                <UiCSGOSkeleton type="line" height="1rem" width="70%" />
              </div>
              <div class="flex flex-col lg:flex-row items-center gap-4">
                <UiCSGOSkeleton type="button" height="3rem" width="8rem" />
                <UiCSGOSkeleton type="button" height="3rem" width="8rem" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 开箱区域骨架 - 完全匹配 SmoothCaseAnimation 布局 -->
      <div class="bg-gradient-to-br from-gray-800/90 to-gray-900/90 rounded-2xl border border-gray-700/30 backdrop-blur-md relative overflow-hidden mb-6">
        <!-- 背景装饰 - 匹配实际组件 -->
        <div class="absolute inset-0 bg-grid-pattern opacity-5"></div>
        <div class="absolute -inset-px bg-gradient-to-r from-primary/20 via-transparent to-secondary/20 rounded-2xl"></div>
        
        <!-- 动画轨道容器 - 匹配实际的 py-8 px-4 -->
        <div class="relative py-8 px-4">
          <!-- 中奖指示线骨架 - 匹配实际尺寸 w-1 h-60 -->
          <div class="absolute left-1/2 top-8 w-1 h-60 -translate-x-1/2 z-20">
            <div class="h-full bg-gradient-to-b from-transparent via-cyan-400/50 to-transparent opacity-80 animate-pulse"></div>
          </div>
          
          <!-- 动画轨道 - 匹配实际的 h-56 py-4 -->
          <div class="relative h-56 overflow-hidden py-4">
            <div class="flex items-center h-full">
              <!-- 动画物品卡片骨架 - 匹配实际尺寸 w-32 h-40 mx-2 -->
              <div
                v-for="i in 10"
                :key="`skeleton-item-${i}`"
                class="flex-shrink-0 mx-2 relative group w-32 h-40"
                :style="{ animationDelay: `${i * 0.1}s` }"
              >
                <!-- 物品卡片 - 匹配实际样式 -->
                <div 
                  class="relative h-full bg-gradient-to-b from-gray-700/80 to-gray-800/80 rounded-lg border-2 overflow-hidden animate-pulse"
                  :style="{ 
                    borderColor: i === 4 ? '#00bcd4' + '80' : (['#b0c3d9', '#5e98d9', '#4b69ff', '#8847ff', '#d32ce6', '#eb4b4b', '#e4ae39'][i % 7] + '80')
                  }"
                >
                  <!-- 稀有度指示条 - 底部位置匹配实际组件 -->
                  <div 
                    class="absolute bottom-0 left-0 right-0 h-1 opacity-80 animate-pulse"
                    :style="{ backgroundColor: ['#b0c3d9', '#5e98d9', '#4b69ff', '#8847ff', '#d32ce6', '#eb4b4b', '#e4ae39'][i % 7] }"
                  ></div>
                  
                  <!-- StatTrak 角标骨架 - 左上角位置 -->
                  <div v-if="i % 3 === 1" class="absolute top-2 left-2 z-10">
                    <div class="bg-secondary/30 rounded-md px-2 py-1 text-xs animate-pulse">
                      <UiCSGOSkeleton type="line" height="0.75rem" width="1.5rem" />
                    </div>
                  </div>

                  <!-- 物品图片骨架 - 匹配实际的 h-24 pt-11 px-2 pb-2 -->
                  <div class="relative h-24 flex items-center justify-center pt-11 px-2 pb-2">
                    <div class="max-w-full max-h-full bg-gray-600/50 rounded animate-pulse w-16 h-12">
                      <!-- 模拟武器轮廓 -->
                      <div class="w-full h-full bg-gradient-to-br from-gray-500/30 to-gray-700/30 rounded"></div>
                    </div>
                  </div>
                  
                  <!-- 物品信息骨架 - 匹配实际的 p-3 space-y-1 -->
                  <div class="p-3 space-y-1">
                    <!-- 饰品名称骨架 - 居中显示 -->
                    <div class="text-center">
                      <UiCSGOSkeleton type="line" height="0.875rem" :width="`${60 + (i % 3) * 20}%`" />
                    </div>
                    
                    <!-- 品质和外观信息骨架 - 两端对齐 -->
                    <div class="flex items-center justify-between text-xs">
                      <UiCSGOSkeleton type="line" height="0.75rem" width="40%" />
                      <UiCSGOSkeleton type="line" height="0.75rem" width="35%" />
                    </div>
                    
                    <!-- 品质信息骨架 -->
                    <div v-if="i % 4 === 0">
                      <UiCSGOSkeleton type="line" height="0.75rem" width="50%" />
                    </div>
                    
                    <!-- 价格信息骨架 -->
                    <div>
                      <UiCSGOSkeleton type="line" height="0.875rem" width="45%" />
                    </div>
                  </div>
                  
                  <!-- 获胜物品特效骨架 -->
                  <div v-if="i === 4" class="absolute inset-0 bg-gradient-to-r from-cyan-400/20 to-blue-400/20 rounded-lg animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 控制区域骨架 - 匹配实际的 mt-8 -->
        <div class="mt-8 text-center pb-8">
          <div class="relative inline-block">
            <!-- 按钮骨架 - 匹配实际尺寸 px-8 py-4 -->
            <div class="px-8 py-4 bg-gradient-to-r from-primary/30 to-secondary/30 text-white font-bold text-lg rounded-xl animate-pulse">
              <UiCSGOSkeleton type="line" height="1.125rem" width="8rem" />
            </div>
            <!-- 按钮光效骨架 -->
            <div class="absolute inset-0 bg-gradient-to-r from-primary/20 to-secondary/20 rounded-xl blur-sm -z-10 animate-pulse"></div>
          </div>
        </div>
      </div>

      <!-- 最近开箱记录骨架 -->
      <div class="bg-gradient-to-br from-gray-800/90 to-gray-900/90 rounded-2xl p-6 border border-gray-700/30 backdrop-blur-md mb-6">
        <!-- 标题骨架 -->
        <div class="flex items-center mb-6">
          <div class="w-6 h-6 bg-secondary/30 rounded mr-3 animate-pulse"></div>
          <UiCSGOSkeleton type="line" height="2rem" width="12rem" />
        </div>
        
        <!-- 模拟真实开箱记录列表布局 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <div v-for="i in 9" :key="i" class="bg-gray-800/50 rounded-lg p-4 hover:bg-gray-800/70 transition-colors duration-200 border border-gray-700/30 animate-pulse" :style="{ animationDelay: `${i * 0.1}s` }">
            <div class="flex items-center gap-3">
              <!-- 饰品图片骨架 -->
              <div class="w-12 h-12 bg-gray-700/50 rounded-lg flex-shrink-0 relative overflow-hidden">
                <div class="absolute inset-0 bg-gradient-to-br from-gray-600/30 to-gray-800/30"></div>
                <!-- 图片闪烁效果 -->
                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse"></div>
              </div>
              
              <!-- 用户信息和饰品信息骨架 -->
              <div class="flex-1 min-w-0 space-y-1">
                <!-- 用户名骨架 -->
                <div class="flex items-center gap-2">
                  <UiCSGOSkeleton type="line" height="1rem" :width="`${60 + (i % 3) * 20}%`" />
                  <!-- 在线状态指示 -->
                  <div v-if="i % 2 === 0" class="w-2 h-2 bg-green-400/50 rounded-full animate-pulse"></div>
                </div>
                
                <!-- 饰品名称骨架 -->
                <UiCSGOSkeleton type="line" height="0.875rem" :width="`${70 + (i % 4) * 15}%`" />
                
                <!-- 价格骨架 -->
                <div class="flex items-center justify-between">
                  <UiCSGOSkeleton type="line" height="0.875rem" :width="`${50 + (i % 2) * 25}%`" />
                  <!-- 时间戳骨架 -->
                  <UiCSGOSkeleton type="line" height="0.75rem" width="3rem" />
                </div>
              </div>
            </div>
            
            <!-- 稀有度装饰 -->
            <div class="absolute top-2 right-2 w-1 h-6 rounded-full animate-pulse"
              :style="{ backgroundColor: ['#b0c3d9', '#5e98d9', '#4b69ff', '#8847ff', '#d32ce6', '#eb4b4b'][i % 6] + '40' }"></div>
          </div>
        </div>
      </div>

      <!-- 可获得物品骨架 -->
      <div class="bg-gray-900/90 rounded-2xl border border-gray-700/40 shadow-2xl p-6 relative overflow-hidden">
        <!-- 背景装饰 -->
        <div class="absolute bottom-0 right-0 w-28 h-28 rounded-full blur-3xl bg-orange-500/8 animate-pulse"></div>
        
        <UiCSGOSkeleton type="category" />
        
        <!-- 稀有度标签骨架 -->
        <div class="flex flex-wrap gap-2 mt-6 mb-6">
          <div v-for="i in 5" :key="i" class="relative">
            <UiCSGOSkeleton type="button" height="2.5rem" width="6rem" />
            <!-- 标签装饰 -->
            <div class="absolute inset-0 rounded-lg border border-yellow-500/20 animate-pulse" :style="{ animationDelay: `${i * 0.1}s` }"></div>
          </div>
        </div>
        
        <!-- 物品网格骨架 -->
        <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
          <div v-for="i in 12" :key="i" class="bg-gray-800/50 rounded-lg p-3">
            <!-- 图片骨架 - 与实际图片容器保持一致 -->
            <div class="aspect-square mb-3 bg-gray-700/30 rounded-lg p-3 relative overflow-hidden">
              <UiCSGOSkeleton type="case" />
              <!-- 稀有度指示条骨架 -->
              <div class="absolute bottom-0 left-0 right-0 h-1 bg-primary/30 animate-pulse"></div>
            </div>
            <UiCSGOSkeleton type="line" height="0.875rem" width="90%" />
            <div class="mt-1 mb-2">
              <UiCSGOSkeleton type="line" height="0.75rem" width="70%" />
            </div>
            <UiCSGOSkeleton type="line" height="0.875rem" width="50%" />
          </div>
        </div>
      </div>

      <!-- 错误信息提示 - 当有错误时显示在骨架图上方 -->
      <Transition name="fade">
        <div v-if="error" class="fixed bottom-6 right-6 bg-red-900/90 border border-red-600/50 rounded-lg p-4 max-w-sm backdrop-blur-sm z-50">
          <div class="flex items-start gap-3">
            <div class="text-red-400 text-xl flex-shrink-0">⚠️</div>
            <div class="flex-1 min-w-0">
              <h3 class="text-red-300 font-medium text-sm mb-1">{{ $t('cases.load_error_title') }}</h3>
              <p class="text-red-200 text-xs mb-3 opacity-90">{{ error }}</p>
              <div class="flex gap-2">
                <button @click="retryLoadData" class="px-3 py-1 bg-red-600 hover:bg-red-500 text-white text-xs rounded transition-colors">
                  <i class="fa fa-refresh mr-1"></i>
                  {{ $t('common.retry') }}
                </button>
                <button @click="dismissError" class="px-3 py-1 bg-gray-600 hover:bg-gray-500 text-white text-xs rounded transition-colors">
                  {{ $t('common.dismiss') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </Transition>
    </div>

    <!-- 主要内容 -->
    <div v-else class="container mx-auto px-4 py-6">
      <!-- 页面头部 - 箱子信息 -->
      <div class="relative bg-gray-900/95 backdrop-blur-md rounded-2xl border border-gray-700/50 mb-6 overflow-hidden">
        <!-- 星空背景效果 -->
        <div class="absolute inset-0 bg-gradient-to-br from-gray-900 via-blue-900/20 to-purple-900/20"></div>
        
        <!-- 星点装饰 -->
        <div class="absolute top-8 left-12 w-1 h-1 bg-white/60 rounded-full animate-pulse"></div>
        <div class="absolute top-16 right-20 w-0.5 h-0.5 bg-blue-300/80 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
        <div class="absolute top-24 left-1/3 w-1.5 h-1.5 bg-purple-300/70 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
        <div class="absolute bottom-20 right-16 w-1 h-1 bg-cyan-300/60 rounded-full animate-pulse" style="animation-delay: 1.5s;"></div>
        <div class="absolute bottom-32 left-20 w-0.5 h-0.5 bg-white/50 rounded-full animate-pulse" style="animation-delay: 2s;"></div>
        <div class="absolute top-1/3 right-1/4 w-1 h-1 bg-blue-200/70 rounded-full animate-pulse" style="animation-delay: 2.5s;"></div>
        
        <!-- 浮动光球装饰 -->
        <div class="absolute top-6 right-8 w-24 h-24 bg-blue-500/10 rounded-full blur-2xl animate-pulse"></div>
        <div class="absolute bottom-8 left-12 w-32 h-32 bg-purple-500/8 rounded-full blur-3xl animate-pulse" style="animation-delay: 1s;"></div>
        <div class="absolute top-1/2 right-1/3 w-16 h-16 bg-cyan-500/12 rounded-full blur-xl animate-pulse" style="animation-delay: 2s;"></div>
        <!-- 左侧浮动光球 -->
        <div class="absolute bottom-5 left-1/4 w-16 h-16 bg-emerald-500/12 rounded-full blur-xl animate-pulse" style="animation-delay: 3s;"></div>
        <div class="absolute top-6 left-8 w-16 h-16 bg-emerald-500/12 rounded-full blur-xl animate-pulse" style="animation-delay: 3s;"></div>
        <div class="absolute bottom-1/3 left-12 w-16 h-16 bg-emerald-500/12 rounded-full blur-xl animate-pulse" style="animation-delay: 3s;"></div>

        <div class="relative p-6 lg:p-8">
          <div class="flex flex-col lg:flex-row items-center lg:items-start gap-8">
            <!-- 箱子图片 -->
            <div class="relative group">
              <div class="w-48 h-48 lg:w-64 lg:h-64 relative">
                <!-- 简化的底层发光效果 - 仅保留核心光晕 -->
                <div class="absolute -inset-2 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-60 transition-all duration-500"></div>
                
                <!-- 简化的箱子图片容器 - 极简无边框设计 -->
                <div class="relative w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-800/60 to-gray-900/80 rounded-xl transition-all duration-300 backdrop-blur-sm overflow-hidden">
                  <!-- 简化的悬停效果 - 仅保留微妙渐变 -->
                  <div class="absolute inset-0 bg-gradient-to-br from-primary/8 to-secondary/8 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl"></div>
                  
                  <img 
                    :src="caseDetail.cover" 
                    :alt="getLocalizedNameForCase(caseDetail, 'name')"
                    class="relative z-10 w-4/5 h-4/5 object-contain transition-transform duration-300 group-hover:scale-105 filter drop-shadow-lg"
                    @error="handleImageError"
                  >
                  
                  <!-- 微妙的光泽效果 - 仅在悬停时显示 -->
                  <div class="absolute top-4 left-4 w-16 h-16 bg-gradient-to-br from-white/10 to-transparent rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 blur-sm"></div>
                </div>
              </div>
            </div>

            <!-- 箱子详情 -->
            <div class="flex-1 text-center lg:text-left">
              <h1 class="text-2xl lg:text-3xl font-bold mb-4 bg-gradient-to-r from-primary to-secondary bg-clip-text text-transparent">
                {{ getLocalizedNameForCase(caseDetail, 'name') }}
              </h1>
              
              <!-- 标签 -->
              <div class="flex flex-wrap gap-2 justify-center lg:justify-start mb-4">
                <span v-if="caseDetail.tag" 
                  class="px-3 py-1 bg-primary/20 text-primary rounded-full text-sm font-medium border border-primary/30">
                  {{ getLocalizedNameForCase(caseDetail, 'tag') }}
                </span>
                <span class="px-3 py-1 bg-blue-500/20 text-blue-400 rounded-full text-sm border border-blue-500/30">
                  ⭐ 4.5
                </span>
                <span class="px-3 py-1 bg-emerald-500/20 text-emerald-400 rounded-full text-sm border border-emerald-500/30">
                  🔓 {{ caseDetail.open_count || 0 }} {{ $t('cases.times_opened') }}
                </span>
              </div>

              <!-- 描述 -->
              <p class="text-gray-300 mb-6 text-sm lg:text-base">
                {{ $t('cases.case_description', { name: getLocalizedNameForCase(caseDetail, 'name') }) }}
              </p>

              <!-- 价格显示 -->
              <div class="inline-flex items-center gap-3 bg-gray-800/50 px-6 py-3 rounded-xl border border-gray-700/50">
                <span class="text-emerald-400 text-xl lg:text-2xl font-bold">
                  ${{ (caseDetail.price || 0).toFixed(2) }}
                </span>
                <span v-if="caseDetail.discount && caseDetail.discount < 100" 
                  class="text-gray-400 text-sm line-through">
                  ${{ ((caseDetail.price || 0) * (100 / (caseDetail.discount || 100))).toFixed(2) }}
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 简化的开箱区域 - 移除多层背景嵌套 -->
      <div ref="openSection" class="mb-8 scroll-mt-4">
        <!-- 直接使用SmoothCaseAnimation组件，让组件自己管理背景 -->
        <SmoothCaseAnimation
          ref="smoothCaseAnimationRef"
          :selected-case="{ ...caseDetail, key: caseKey }"
          :case-items="caseItems"
          @opening-start="onOpeningStart"
          @case-opened="handleCaseOpened"
        />
      </div>

      <!-- 最近开箱记录 - 重新设计版 -->
      <div class="mb-8">
        <!-- 主容器 - 使用更低调的背景色 -->
        <div class="relative bg-gray-900/60 rounded-2xl border border-gray-700/50 backdrop-blur-sm overflow-hidden">
          <!-- 背景装饰网格 - 降低透明度 -->
          <div class="absolute inset-0 bg-grid-pattern opacity-3"></div>
          
          <!-- 渐变边框装饰 - 更加微妙 -->
          <div class="absolute -inset-px bg-gradient-to-r from-gray-600/10 via-transparent to-gray-600/10 rounded-2xl"></div>
          
          <!-- 内容区域 -->
          <div class="relative p-6">
            <!-- 标题区域 - 增强设计 -->
            <div class="flex items-center justify-between mb-6">
              <h2 class="text-xl font-bold flex items-center text-white">
                <div class="w-8 h-8 mr-3 bg-secondary/20 rounded-lg flex items-center justify-center border border-secondary/30">
                  <span class="text-secondary text-lg">📜</span>
                </div>
                {{ $t('cases.recent_openings') }}
              </h2>
              
              <!-- 实时更新指示器 -->
              <div class="flex items-center gap-2 text-xs text-gray-400">
                <div class="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                <span>{{ $t('cases.live_updates') }}</span>
              </div>
            </div>

            <!-- Loading状态 - 增强骨架图 -->
            <div v-if="loadingRecords" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div v-for="i in 6" :key="i" 
                class="relative bg-gray-800/30 rounded-xl p-4 border border-gray-700/40 overflow-hidden"
                :style="{ animationDelay: `${i * 0.1}s` }">
                <!-- 骨架图背景装饰 - 更加微妙 -->
                <div class="absolute inset-0 bg-gradient-to-br from-gray-700/5 to-gray-800/5 animate-pulse"></div>
                
                <div class="relative flex items-center gap-4">
                  <!-- 饰品图片骨架 -->
                  <div class="w-24 h-20 bg-gray-700/50 rounded-lg flex-shrink-0 relative overflow-hidden">
                    <div class="absolute inset-0 bg-gradient-to-br from-gray-600/30 to-gray-800/30 animate-pulse"></div>
                    <!-- 稀有度指示条骨架 -->
                    <div class="absolute bottom-0 left-0 right-0 h-1 bg-primary/30 animate-pulse"></div>
                  </div>
                  
                  <!-- 信息骨架 -->
                  <div class="flex-1 space-y-2">
                    <UiCSGOSkeleton type="line" height="1rem" width="80%" />
                    <UiCSGOSkeleton type="line" height="0.875rem" width="60%" />
                    <div class="flex items-center justify-between">
                      <UiCSGOSkeleton type="line" height="0.75rem" width="40%" />
                      <UiCSGOSkeleton type="line" height="0.75rem" width="30%" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- 开箱记录列表 - 专业重新设计 -->
            <div v-else-if="recentOpenings.length > 0" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              <div v-for="record in recentOpenings.slice(0, 12)" :key="record.id" 
                class="group relative bg-gray-800/30 rounded-xl border border-gray-700/40 overflow-hidden transition-all duration-300 hover:border-gray-600/60 hover:scale-[1.01] hover:-translate-y-0.5">
                
                <!-- 背景装饰效果 - 更加微妙 -->
                <div class="absolute inset-0 bg-gradient-to-br from-gray-700/10 to-gray-800/10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                <!-- 稀有度发光边框 - 降低强度 -->
                <div class="absolute inset-0 rounded-xl opacity-0 group-hover:opacity-20 transition-opacity duration-300"
                  :style="{ boxShadow: `0 0 15px ${record.item_info?.item_rarity?.rarity_color || 'var(--rarity-common)'}30` }"></div>
                
                <div class="relative p-4">
                  <!-- 主要布局：饰品图片 + 信息 -->
                  <div class="flex items-start gap-4">
                    <!-- 饰品图片容器 - 修复高度和对齐 -->
                    <div class="relative w-24 h-20 flex-shrink-0">
                      <!-- 图片容器 -->
                      <div class="w-full h-full rounded-lg bg-gray-700/30 flex items-center justify-center overflow-hidden border-2 border-gray-600/30 group-hover:border-primary/50 transition-all duration-300">
                        <img :src="record.item_info?.image" 
                          :alt="getLocalizedSkinName(record)"
                          class="w-full h-full object-contain transition-transform duration-300 p-1.5 group-hover:scale-110"
                          @error="handleImageError">
                      </div>
                      
                      <!-- 稀有度指示条 - 底部全宽 -->
                      <div class="absolute bottom-0 left-0 right-0 h-1 rounded-b-lg opacity-80"
                        :style="{ backgroundColor: record.item_info?.item_rarity?.rarity_color || 'var(--rarity-common)' }"></div>
                      
                      <!-- StatTrak™ 标识 - 统一样式 -->
                      <div v-if="isStatTrakItem(record)" class="absolute -top-1 -left-1 z-10">
                        <div class="bg-secondary/90 rounded-lg px-1.5 py-0.5 text-xs font-bold text-white shadow-lg backdrop-blur-sm border border-secondary/50 flex items-center gap-1">
                          <span class="text-yellow-300 text-xs">⚡</span>
                          <span>ST</span>
                        </div>
                      </div>
                    </div>
                    
                    <!-- 信息区域 -->
                    <div class="flex-1 min-w-0 space-y-2.5">
                      <!-- 饰品名称 -->
                      <h3 class="text-white text-sm font-semibold leading-tight group-hover:text-primary transition-colors duration-300 line-clamp-2">
                        {{ getLocalizedSkinName(record) }}
                      </h3>
                      
                      <!-- 价格和稀有度 -->
                      <div class="flex items-center justify-between">
                        <!-- 价格 -->
                        <div class="flex items-center text-emerald-400">
                          <div class="w-2 h-2 mr-1.5 bg-emerald-400/30 rounded-full flex items-center justify-center">
                            <div class="w-1 h-1 bg-emerald-400 rounded-full"></div>
                          </div>
                          <span class="text-sm font-bold">
                            ${{ (record.item_info?.item_price?.price || 0).toFixed(2) }}
                          </span>
                        </div>
                        
                        <!-- 稀有度标识 -->
                        <div class="flex items-center">
                          <div class="w-2 h-2 rounded-full border border-white/20"
                            :style="{ backgroundColor: record.item_info?.item_rarity?.rarity_color || 'var(--rarity-common)' }"></div>
                        </div>
                      </div>
                      
                      <!-- 用户信息 -->
                      <div class="flex items-center gap-2 pt-1 border-t border-gray-700/30">
                        <!-- 用户头像 -->
                        <div class="w-6 h-6 relative flex-shrink-0">
                          <img :src="getUserAvatar(record)" 
                            :alt="getUserName(record)"
                            class="w-full h-full rounded-full object-cover bg-gray-600/50 border border-gray-600/50 group-hover:border-primary/50 transition-colors duration-300"
                            @error="handleAvatarError">
                        </div>
                        
                        <!-- 用户名和时间 -->
                        <div class="flex-1 min-w-0 flex items-center justify-between">
                          <span class="text-gray-300 text-xs font-medium truncate">
                            {{ getUserName(record) }}
                          </span>
                          <span class="text-gray-500 text-xs whitespace-nowrap ml-2">
                            {{ formatOpeningTime(record) }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                
                <!-- 稀有度装饰光效 - 右侧边框 -->
                <div class="absolute top-0 right-0 w-1 h-full opacity-60 transition-opacity duration-300 group-hover:opacity-100"
                  :style="{ backgroundColor: record.item_info?.item_rarity?.rarity_color || 'var(--rarity-common)' }"></div>
                
                <!-- 悬停时的粒子效果装饰 -->
                <div class="absolute top-2 right-2 w-1 h-1 bg-primary/60 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-300 animate-pulse"></div>
                <div class="absolute bottom-2 left-2 w-0.5 h-0.5 bg-secondary/60 rounded-full opacity-0 group-hover:opacity-100 transition-all duration-500 animate-pulse" style="animation-delay: 0.2s;"></div>
              </div>
            </div>

            <!-- 无记录状态 - 重新设计 -->
            <div v-else class="text-center py-16">
              <!-- 图标容器 -->
              <div class="w-20 h-20 mx-auto mb-6 relative">
                <!-- 背景装饰 -->
                <div class="absolute inset-0 bg-gradient-to-br from-primary/20 to-secondary/20 rounded-full blur-xl"></div>
                <div class="relative w-full h-full bg-gray-800/50 rounded-full border border-gray-700/50 flex items-center justify-center backdrop-blur-sm">
                  <span class="text-4xl">📦</span>
                </div>
                <!-- 装饰光点 -->
                <div class="absolute -top-1 -right-1 w-3 h-3 bg-primary/60 rounded-full animate-pulse"></div>
                <div class="absolute -bottom-1 -left-1 w-2 h-2 bg-secondary/60 rounded-full animate-pulse" style="animation-delay: 0.5s;"></div>
              </div>
              
              <!-- 文字内容 -->
              <h3 class="text-xl font-bold text-white mb-3">{{ $t('cases.no_recent_openings_title') }}</h3>
              <p class="text-gray-400 mb-6 max-w-md mx-auto leading-relaxed">{{ $t('cases.no_recent_openings_description') }}</p>
              
              <!-- 行动按钮 -->
              <button @click="scrollToOpenSection" 
                class="relative inline-flex items-center gap-2 bg-gradient-to-r from-primary to-primary-dark text-black font-bold px-8 py-4 rounded-xl transition-all duration-300 hover:scale-105 hover:shadow-lg group overflow-hidden">
                <!-- 按钮背景装饰 -->
                <div class="absolute inset-0 bg-gradient-to-r from-primary-light/20 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                
                <span class="relative text-lg">🔑</span>
                <span class="relative font-bold">{{ $t('cases.be_first_to_open') }}</span>
                
                <!-- 按钮光效 -->
                <div class="absolute inset-0 bg-gradient-to-r from-primary/20 to-primary-dark/20 rounded-xl blur-sm -z-10 opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 可获得物品 -->
      <div class="mb-8">
        <div class="bg-gray-900/95 backdrop-blur-md rounded-2xl p-6 border border-gray-700/50">
          <h2 class="text-xl font-bold mb-6 flex items-center text-white">
            <span class="text-yellow-500 mr-3">🎁</span>
            {{ $t('cases.available_items') }}
          </h2>

          <!-- Loading状态 -->
          <div v-if="loadingItems">
            <!-- 标签导航骨架 -->
            <div class="flex flex-wrap gap-2 mb-6">
              <UiCSGOSkeleton v-for="i in 5" :key="i" type="button" height="2.5rem" width="6rem" />
            </div>
            
            <!-- 物品网格骨架 -->
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <div v-for="i in 12" :key="i" class="bg-gray-800/50 rounded-lg p-3">
                <div class="aspect-square mb-3">
                  <UiCSGOSkeleton type="case" />
                </div>
                <UiCSGOSkeleton type="line" height="0.875rem" width="90%" />
                <div class="mt-1 mb-2">
                  <UiCSGOSkeleton type="line" height="0.75rem" width="70%" />
                </div>
                <UiCSGOSkeleton type="line" height="0.875rem" width="50%" />
              </div>
            </div>
          </div>

          <!-- 稀有度标签页 -->
          <div v-else-if="caseItems.length > 0">
            <!-- 标签导航 -->
            <div class="flex flex-wrap gap-2 mb-6 py-2 overflow-x-auto">
              <button @click="activeTab = 'all'" 
                :class="activeTab === 'all' ? 'bg-primary text-black' : 'bg-gray-800/50 text-gray-300 hover:bg-gray-700/50'"
                class="px-4 py-2 rounded-lg whitespace-nowrap transition-colors duration-200 border border-gray-600/50">
                {{ $t('skins.all') }} ({{ totalItemsCount }})
              </button>
              <button v-for="rarity in caseItems" :key="rarity.rarity_id"
                @click="activeTab = rarity.rarity_id.toString()"
                :style="{ 
                  backgroundColor: activeTab === rarity.rarity_id.toString() ? rarity.rarity_color : 'rgba(55, 65, 81, 0.5)',
                  borderColor: rarity.rarity_color + '80',
                  color: activeTab === rarity.rarity_id.toString() ? '#000' : rarity.rarity_color
                }"
                class="px-4 py-2 rounded-lg border whitespace-nowrap transition-all duration-200 hover:scale-105">
                {{ getLocalizedNameForCase(rarity, 'rarity_name') }} ({{ rarity.count_percentage }}%)
              </button>
            </div>

            <!-- 物品网格 -->
            <div class="grid grid-cols-2 md:grid-cols-4 lg:grid-cols-6 gap-4">
              <div v-for="item in displayedItems" :key="item.id"
                class="bg-gray-800/50 rounded-lg p-3 hover:bg-gray-800/70 transition-all duration-200 border border-gray-700/30 hover:border-primary/50 group">
                <!-- 图片容器 - 调整比例和内边距 -->
                <div class="aspect-square mb-3 relative overflow-hidden rounded-lg bg-gray-700/30 p-3">
                  <img :src="item.item_info?.image" 
                    :alt="getLocalizedNameForCase(item.item_info, 'name')"
                    class="w-full h-full object-contain transition-transform duration-200 group-hover:scale-110"
                    @error="handleImageError">
                  
                  <!-- 稀有度指示条 -->
                  <div class="absolute bottom-0 left-0 right-0 h-1" 
                    :style="{ backgroundColor: item.rarity_color }"></div>
                  
                  <!-- StatTrak™ 标识 - 统一样式 -->
                  <div v-if="isStatTrakItem(item)" class="absolute top-2 left-2 z-10">
                    <div class="bg-secondary/90 rounded-md px-1.5 py-0.5 text-xs font-bold text-white shadow-lg backdrop-blur-sm border border-secondary/50">
                      <span class="mr-0.5">⚡</span>ST
                    </div>
                  </div>
                  
                  <!-- 概率显示 -->
                  <div class="absolute top-2 right-2 bg-gray-900/80 px-2 py-1 rounded text-xs text-white">
                    {{ (item.chance || 0).toFixed(2) }}%
                  </div>
                </div>
                
                <h3 class="text-white text-sm font-medium mb-1 truncate">
                  {{ getLocalizedNameForCase(item.item_info, 'name') }}
                </h3>
                <p class="text-gray-400 text-xs truncate mb-2">
                  {{ getLocalizedNameForCase(item.item_info?.item_exterior, 'exterior_name') }}
                </p>
                <p class="text-emerald-400 text-sm font-medium">
                  ${{ (item.item_info?.item_price?.price || 0).toFixed(2) }}
                </p>
              </div>
            </div>
          </div>

          <!-- 无物品状态 -->
          <div v-else class="text-center py-12">
            <div class="w-16 h-16 mx-auto mb-4 relative">
              <div class="absolute inset-0 bg-yellow-500/20 rounded-full animate-pulse"></div>
              <div class="relative w-full h-full flex items-center justify-center text-3xl">
                🎁
              </div>
            </div>
            <h3 class="text-lg font-medium text-white mb-2">{{ $t('cases.no_items_title') }}</h3>
            <p class="text-gray-400 mb-4">{{ $t('cases.no_items_description') }}</p>
            <button @click="fetchCaseItems" class="bg-primary hover:bg-primary/90 text-black font-bold px-6 py-3 rounded-lg transition-colors duration-200">
              🔄 {{ $t('cases.reload_items') }}
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 开箱结果弹出框 -->
    <Transition name="fade">
      <div v-if="showResult" class="fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 px-4">
        <div class="bg-gray-900/95 rounded-2xl border border-green-600/50 shadow-2xl p-8 text-center max-w-md w-full relative">
          <!-- 装饰性背景 -->
          <div class="absolute -inset-1 bg-gradient-to-r from-green-500/20 to-blue-500/20 blur-xl opacity-50 rounded-2xl"></div>
          
          <div class="relative">
            <!-- 成功图标 -->
            <div class="w-20 h-20 mx-auto mb-4 relative">
              <div class="absolute inset-0 bg-green-500/20 rounded-full animate-pulse"></div>
              <div class="relative w-full h-full flex items-center justify-center text-4xl">
                🎉
              </div>
            </div>
            
            <h2 class="text-2xl font-bold text-white mb-3">{{ $t('cases.opening_result_title') }}</h2>
            <p class="text-gray-300 mb-4">
              {{ $t('cases.opening_result_description') }}
            </p>
            
            <!-- 物品信息 -->
            <div v-if="openingResult" class="mb-4">
              <div class="text-white font-medium text-lg mb-2">
                {{ getApiItemName(openingResult) }}
              </div>
              <div class="flex items-center justify-center gap-2">
                <img 
                  :src="openingResult.image" 
                  :alt="getApiItemName(openingResult)"
                  class="w-16 h-16 object-contain rounded-lg bg-gray-700/30"
                  @error="handleImageError">
                <div class="text-center">
                  <div class="text-emerald-400 font-bold text-xl">
                    ${{ (openingResult.price || 0).toFixed(2) }}
                  </div>
                  <div class="text-gray-400 text-sm">
                    {{ getApiItemExterior(openingResult) }}
                  </div>
                </div>
              </div>
            </div>

            <div class="flex flex-col sm:flex-row gap-3 justify-center">
              <button @click="closeResultPopup" class="bg-primary hover:bg-primary/90 text-black font-bold px-6 py-3 rounded-lg transition-colors duration-200">
                ✅ {{ $t('common.close') }}
              </button>
              <button v-if="openingResult" @click="viewInInventory" class="bg-gray-700 hover:bg-gray-600 text-white px-6 py-3 rounded-lg transition-colors duration-200">
                👁️ {{ $t('cases.view_in_inventory') }}
              </button>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup lang="ts">
import { caseApi } from '~/services/case-api'
import { useCaseData } from '~/composables/useCaseData'
import SmoothCaseAnimation from '~/components/case/SmoothCaseAnimation.vue'
import { ref, onMounted, onUnmounted } from 'vue'
import { useSocketRoomManager, socketRooms, socketEvents } from '~/utils/socket-manager'

// 页面配置
definePageMeta({
  title: 'Case Detail',
  key: route => `case-${route.params.key}`
})

// 路由和国际化
const route = useRoute()
const { $i18n } = useNuxtApp()
const { locale } = useI18n()
const localePath = useLocalePath()

// 箱子键值
const caseKey = route.params.key as string

// 响应式状态
const isLoading = ref(true)
const error = ref<string | null>(null)
const activeTab = ref('all')

// 使用增强的store
const caseStore = useCaseStore()
const userStore = useUserStore()
const socketRoomManager = useSocketRoomManager()

// 组件引用
const openSection = ref<HTMLElement>()
const smoothCaseAnimationRef = ref<InstanceType<typeof SmoothCaseAnimation>>()

// 弹出框状态管理
const showResult = ref(false)
const openingResult = ref<any>(null)

// 计算属性
const hasError = computed(() => !!error.value)
const errorMessage = computed(() => error.value || '')

// 数据准备状态 - 从store获取数据状态
const isDataReady = computed(() => {
  const storeReady = caseStore.isCaseDataReady(caseKey)
  const notLoading = !isLoading.value
  
  // console.log(`[Cases详情页] 数据准备状态检查 - ${caseKey}:`)
  // console.log('  Store Ready:', storeReady)
  // console.log('  Not Loading:', notLoading)
  // console.log('  Case Detail:', !!caseStore.getCaseDetail(caseKey))
  // console.log('  Case Items:', caseStore.getCaseItems(caseKey)?.length || 0)
  
  return storeReady && notLoading
})

// 从store获取数据
const caseDetail = computed(() => caseStore.getCaseDetail(caseKey) || {})
const caseItems = computed(() => caseStore.getCaseItems(caseKey) || [])
const recentOpenings = computed(() => caseStore.getOpeningRecords(caseKey) || [])

// 加载状态
const loadingRecords = computed(() => caseStore.isLoading(caseKey, 'records'))
const loadingItems = computed(() => caseStore.isLoading(caseKey, 'items'))

// 获取本地化名称的响应式函数
const getLocalizedNameForCase = (item: any, fieldName: string): string => {
  if (!item) return ''
  
  const currentLocale = locale.value === 'zh-hans' ? 'zh_hans' : locale.value
  const itemWithI18n = item as any
  
  // 获取本地化字段
  let localizedValue = item[fieldName] || ''
  
  if (currentLocale === 'zh_hans') {
    localizedValue = itemWithI18n[`${fieldName}_zh_hans`] || itemWithI18n[`${fieldName}_zh`] || item[fieldName] || ''
  } else if (currentLocale === 'en') {
    localizedValue = itemWithI18n[`${fieldName}_en`] || item[fieldName] || ''
  }
  
  // 特殊处理标签（保留原有逻辑）
  if (!localizedValue && fieldName === 'tag' && item.tag) {
    const isZhHans = locale.value === 'zh-hans'
    const tagMappings: Record<string, string> = {
      'HOT': isZhHans ? '热门' : 'Hot',
      'NEW': isZhHans ? '新品' : 'New',
      'SELL': isZhHans ? '折扣' : 'Sale',
      'SPECIAL': isZhHans ? '特别' : 'Special',
      'FREE': isZhHans ? '免费' : 'Free'
    }
    return tagMappings[item.tag] || item.tag
  }
  
  return localizedValue
}

// 获取开箱记录的本地化饰品名称
const getLocalizedSkinName = (record: any): string => {
  if (!record) return $i18n.t('skins.unknown')
  
  const isZhHans = locale.value === 'zh-hans'
  
  // 根据真实API数据结构，item_info包含物品信息
  if (record.item_info) {
    const itemInfo = record.item_info
    
    if (isZhHans) {
      // 中文优先级：name_zh_hans > name
      return itemInfo.name_zh_hans || itemInfo.name || $i18n.t('skins.unknown')
    } else {
      // 英文优先级：name_en > name
      return itemInfo.name_en || itemInfo.name || $i18n.t('skins.unknown')
    }
  }
  
  return $i18n.t('skins.unknown')
}

// 总物品数量
const totalItemsCount = computed(() => {
  return caseItems.value.reduce((total, rarity) => total + (rarity.items?.length || 0), 0)
})

// 当前显示的物品
const displayedItems = computed(() => {
  if (activeTab.value === 'all') {
    return caseItems.value.flatMap(rarity => 
      (rarity.items || []).map((item: any) => ({
        ...item,
        rarity_color: rarity.rarity_color
      }))
    ).sort((a, b) => (b.item_info?.item_price?.price || 0) - (a.item_info?.item_price?.price || 0))
  }
  
  const selectedRarity = caseItems.value.find(r => r.rarity_id.toString() === activeTab.value)
  return selectedRarity?.items?.map((item: any) => ({
    ...item,
    rarity_color: selectedRarity.rarity_color
  })) || []
})

// 获取标签样式
const getTagClass = (tag: string) => {
  if (!tag) return 'bg-gray-500/20 text-gray-400'
  
  const tagClasses: Record<string, string> = {
    'HOT': 'bg-orange-500/20 text-orange-400',
    'NEW': 'bg-emerald-500/20 text-emerald-400',
    'SELL': 'bg-red-500/20 text-red-400',
    'SPECIAL': 'bg-purple-500/20 text-purple-400',
    'FREE': 'bg-blue-500/20 text-blue-400'
  }
  
  return tagClasses[tag] || 'bg-gray-500/20 text-gray-400'
}

// 获取星星位置
const getStarPosition = (index: number) => {
  const positions = [
    { top: '10%', left: '15%' },
    { top: '20%', right: '10%' },
    { bottom: '25%', left: '20%' },
    { bottom: '15%', right: '15%' },
    { top: '60%', left: '5%' }
  ]
  return positions[index - 1] || {}
}

// 滚动到开箱区域
const scrollToOpenSection = () => {
  openSection.value?.scrollIntoView({ behavior: 'smooth', block: 'start' })
}

// 开箱开始处理
const onOpeningStart = () => {
  console.log('开箱开始 - 页面监听到开箱开始事件')
  // 移除 isOpeningPage 状态切换，让动画组件自己处理
}

// 处理开箱结果 - 直接使用 API 返回的数据
const handleCaseOpened = async (result: any) => {
  try {
    console.log('开箱成功，API返回数据:', result)
    
    // 移除手动刷新开箱记录，由Socket统一处理更新
    // await fetchRecentOpenings() // 已移除：开箱记录由Socket实时更新
    
    // 显示开箱结果弹出框 - 直接使用 API 数据
    showResult.value = true
    openingResult.value = result
    
    console.log('弹出框显示数据:', result)
  } catch (error) {
    console.error('处理开箱结果失败:', error)
  }
}

// 关闭结果弹出框
const closeResultPopup = () => {
  showResult.value = false
  openingResult.value = null
  
  // 用户关闭弹出框后，重置动画组件的滚动位置
  console.log('👋 用户关闭弹出框，重置滚动位置')
  nextTick(() => {
    try {
      // 调用动画组件的重置方法
      (smoothCaseAnimationRef.value as any)?.resetScrollPosition?.()
    } catch (error) {
      console.warn('⚠️ 重置滚动位置失败:', error)
    }
  })
}

// 查看物品在背包中的信息
const viewInInventory = () => {
  if (openingResult.value) {
    // 使用 API 返回的数据结构 (OpenCaseProcessedItem)
    const itemId = openingResult.value.id || openingResult.value.uid
    const itemType = openingResult.value.itemType
    const itemKey = openingResult.value.pid
    
    // 跳转到背包页面
    navigateTo({
      path: '/inventory',
      query: {
        item_id: itemId,
        item_type: itemType,
        item_key: itemKey
      }
    })
  }
}

// 处理图片错误
const handleImageError = (event: Event) => {
  const img = event.target as HTMLImageElement
  img.src = '/favicon.ico'
}

// 处理头像错误
const handleAvatarError = (event: Event) => {
  const img = event.target as HTMLImageElement
  // 使用默认头像
  img.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNDAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA0MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMjAiIGN5PSIyMCIgcj0iMjAiIGZpbGw9IiM0QjVTNjMiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIxNiIgcj0iNiIgZmlsbD0iIzlDQTNCMiIvPgo8cGF0aCBkPSJNMTAgMzJDMTAgMjYuNDc3MiAxNC40NzcyIDIyIDIwIDIyQzI1LjUyMjggMjIgMzAgMjYuNDc3MiAzMCAzMlYzNkgxMFYzMloiIGZpbGw9IiM5Q0EzQjIiLz4KPC9zdmc+'
}

// 获取用户头像
const getUserAvatar = (record: any): string => {
  // 根据真实API数据结构，user_info.profile.avatar包含头像信息
  if (record.user_info?.profile?.avatar) {
    return record.user_info.profile.avatar
  }
  
  // 如果没有头像，生成基于用户名的头像
  const userName = getUserName(record)
  const firstChar = userName.charAt(0).toUpperCase()
  const colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8']
  const colorIndex = userName.charCodeAt(0) % colors.length
  const bgColor = colors[colorIndex]
  
  // 生成SVG头像
  return `data:image/svg+xml;base64,${btoa(`
    <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
      <circle cx="20" cy="20" r="20" fill="${bgColor}"/>
      <text x="20" y="28" font-family="Arial, sans-serif" font-size="16" font-weight="bold" 
            text-anchor="middle" fill="white">${firstChar}</text>
    </svg>
  `)}`
}

// 获取用户名称
const getUserName = (record: any): string => {
  // 根据真实API数据结构，user_info.profile.nickname包含用户昵称
  if (record.user_info?.profile?.nickname) {
    return record.user_info.profile.nickname
  }
  
  return $i18n.t('cases.anonymous_user')
}

// 格式化开箱时间
const formatOpeningTime = (record: any): string => {
  try {
    // 根据真实API数据结构，create_time包含开箱时间
    const timeValue = record.create_time
    
    if (!timeValue) {
      return $i18n.t('cases.time_unknown')
    }
    
    // 处理时间格式
    const date = new Date(timeValue)
    
    if (isNaN(date.getTime())) {
      return $i18n.t('cases.time_unknown')
    }
    
    const now = new Date()
    const diffMs = now.getTime() - date.getTime()
    const diffMinutes = Math.floor(diffMs / (1000 * 60))
    const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))
    
    if (diffMinutes < 1) {
      return $i18n.t('cases.time_just_now')
    } else if (diffMinutes < 60) {
      return $i18n.t('cases.time_minutes_ago', { minutes: diffMinutes })
    } else if (diffHours < 24) {
      return $i18n.t('cases.time_hours_ago', { hours: diffHours })
    } else if (diffDays < 7) {
      return $i18n.t('cases.time_days_ago', { days: diffDays })
    } else {
      // 超过一周显示具体日期
      return date.toLocaleDateString(locale.value === 'zh-hans' ? 'zh-CN' : 'en-US')
    }
  } catch (error) {
    console.warn('格式化开箱时间失败:', error)
    return $i18n.t('cases.time_unknown')
  }
}

// 检查是否是StatTrak物品 - 统一判断逻辑，支持多种数据结构
const isStatTrakItem = (item: any): boolean => {
  if (!item) return false
  
  // 检查多种可能的StatTrak标识字段
  const hasStatTrak = item.is_stattrak || 
                     item.stattrak || 
                     item.statTrak ||
                     item.item_info?.is_stattrak ||
                     item.item_info?.stattrak ||
                     item.item_info?.statTrak
  
  // 检查item_quality.quality_name字段（开箱记录数据结构）
  if (item.item_info?.item_quality?.quality_name) {
    const qualityName = item.item_info.item_quality.quality_name
    if (qualityName.includes('StatTrak™') || qualityName.includes('StatTrak')) {
      return true
    }
  }
  
  // 检查名称中是否包含StatTrak
  const nameFields = [
    item.name,
    item.nameZhHans,
    item.nameEn,
    item.item_info?.name,
    item.item_info?.nameZhHans,
    item.item_info?.nameEn,
    getLocalizedNameForCase(item, 'name'),
    getLocalizedNameForCase(item.item_info, 'name'),
    getLocalizedSkinName(item) // 兼容开箱记录的名称获取方式
  ]
  
  const hasStatTrakInName = nameFields.some(name => 
    name && (name.includes('StatTrak™') || name.includes('StatTrak') || name.includes('ST'))
  )
  
  return !!(hasStatTrak || hasStatTrakInName)
}

// 获取物品品相
const getItemCondition = (record: any): string => {
  try {
    // 根据真实API数据结构，item_info.item_exterior包含品相信息
    if (record.item_info?.item_exterior) {
      const isZhHans = locale.value === 'zh-hans'
      return isZhHans 
        ? record.item_info.item_exterior.exterior_name_zh_hans || record.item_info.item_exterior.exterior_name || ''
        : record.item_info.item_exterior.exterior_name_en || record.item_info.item_exterior.exterior_name || ''
    }
    
    return ''
  } catch (error) {
    console.warn('获取物品品相失败:', error)
    return ''
  }
}

// 重试加载数据
const retryLoadData = async () => {
  try {
    isLoading.value = true
    error.value = null
    
    await Promise.all([
      fetchCaseData(),
      fetchCaseItems()
    ])
    
    // 获取非关键数据
    fetchRecentOpenings().catch(err => {
      console.warn('获取开箱记录失败，但不影响主要功能:', err)
    })
  } catch (err) {
    console.error('重试加载数据失败:', err)
    error.value = err instanceof Error ? err.message : '重试加载失败'
  } finally {
    isLoading.value = false
  }
}

// 关闭错误提示
const dismissError = () => {
  error.value = null
}

// 获取 API 物品名称（支持多语言）
const getApiItemName = (item: any): string => {
  if (!item) return $i18n.t('skins.unknown')
  
  const isZhHans = locale.value === 'zh-hans'
  
  if (isZhHans) {
    return item.nameZhHans || item.name || item.nameEn || $i18n.t('skins.unknown')
  } else {
    return item.nameEn || item.name || item.nameZhHans || $i18n.t('skins.unknown')
  }
}

// 获取 API 物品外观
const getApiItemExterior = (item: any): string => {
  if (!item?.exterior) return ''
  
  const isZhHans = locale.value === 'zh-hans'
  
  if (isZhHans) {
    return item.exterior.nameZhHans || item.exterior.name || item.exterior.nameEn || ''
  } else {
    return item.exterior.nameEn || item.exterior.name || item.exterior.nameZhHans || ''
  }
}

// 获取箱子数据（使用store）
const fetchCaseData = async () => {
  try {
    error.value = null
    await caseStore.fetchCaseDetail(caseKey)
    console.log('获取箱子详情成功:', {
      caseKey,
      detail: caseStore.getCaseDetail(caseKey)
    })
  } catch (err) {
    console.error('获取箱子详情失败:', err)
    error.value = err instanceof Error ? err.message : '获取箱子详情失败'
  }
}

// 获取箱子物品（使用store）
const fetchCaseItems = async () => {
  try {
    console.log('=== 开始获取箱子物品 ===')
    console.log('箱子Key:', caseKey)
    
    await caseStore.fetchCaseItems(caseKey)
    
    const items = caseStore.getCaseItems(caseKey)
    console.log('✅ 获取箱子物品成功:')
    console.log('  箱子Key:', caseKey)
    console.log('  物品数量:', items.length)
    console.log('  第一个稀有度组示例:', items[0] ? JSON.stringify(items[0], null, 2) : '无稀有度组')
  } catch (err) {
    console.error('❌ 获取箱子物品异常:', err)
  } finally {
    console.log('=== 箱子物品获取完成 ===')
  }
}

// 获取最近开箱记录（使用store）
const fetchRecentOpenings = async () => {
  try {
    await caseStore.fetchOpeningRecords(caseKey)
    
    const records = caseStore.getOpeningRecords(caseKey)
    console.log('获取开箱记录成功:', {
      caseKey,
      recordsCount: records.length
    })
  } catch (err) {
    console.error('获取开箱记录失败:', err)
  }
}

// SEO头部配置
const updateSEO = () => {
  const caseName = getLocalizedNameForCase(caseDetail.value, 'name') || 'Unknown Case'
  
  useHead({
    title: () => $i18n.t('cases.case_detail_title', { name: caseName }),
    meta: [
      {
        name: 'description',
        content: () => $i18n.t('cases.case_detail_description', { name: caseName })
      },
      {
        name: 'keywords',
        content: `CSGO, CS:GO, case, ${caseName}, skins, gaming, weapons`
      },
      {
        property: 'og:title',
        content: () => $i18n.t('cases.case_detail_title', { name: caseName })
      },
      {
        property: 'og:description',
        content: () => $i18n.t('cases.case_detail_description', { name: caseName })
      },
      {
        property: 'og:image',
        content: () => caseDetail.value.cover || ''
      }
    ],
    link: [
      {
        rel: 'canonical',
        href: () => `${useRuntimeConfig().public.siteUrl}${localePath(`/cases/${caseKey}`)}`
      }
    ]
  })
}

// Socket事件处理函数 - 增强版本
const handleCaseRecordsUpdate = (event: Event) => {
  try {
    const customEvent = event as CustomEvent
    const { action, data } = customEvent.detail

    console.log(`[Cases详情页] 收到开箱记录更新 - ${caseKey}:`, action, data)

    // 更新开箱记录数据
    if (Array.isArray(data)) {
      const relevantRecords = data.filter(record => {
        const recordCaseKey = record.case_info?.key || record.case_info?.case_key || record.case_key
        return recordCaseKey === caseKey
      })

      if (relevantRecords.length > 0) {
        console.log(`[Cases详情页] 找到 ${relevantRecords.length} 条相关记录`)
        caseStore.updateOpeningRecords(caseKey, relevantRecords)

        // 触发页面更新事件
        window.dispatchEvent(new CustomEvent('case-records-updated', {
          detail: { caseKey, count: relevantRecords.length, source: 'socket' }
        }))
      }
    } else if (data) {
      const recordCaseKey = data.case_info?.key || data.case_info?.case_key || data.case_key

      if (recordCaseKey === caseKey) {
        console.log(`[Cases详情页] 添加新的开箱记录: ${data.id}`)
        caseStore.addOpeningRecord(caseKey, data)

        // 触发页面更新事件
        window.dispatchEvent(new CustomEvent('case-records-updated', {
          detail: { caseKey, recordId: data.id, source: 'socket' }
        }))
      } else {
        console.log(`[Cases详情页] 记录不属于当前箱子 ${caseKey}, 记录箱子: ${recordCaseKey}`)
      }
    }
  } catch (error) {
    console.error('[Cases详情页] 处理开箱记录更新失败:', error)
  }
}

// 处理箱子统计更新
const handleCaseStatsUpdate = (event: Event) => {
  try {
    const customEvent = event as CustomEvent
    const data = customEvent.detail?.data

    console.log(`[Cases详情页] 收到箱子统计更新 - ${caseKey}:`, data)

    if (data && (data.case_key === caseKey || data.key === caseKey)) {
      // 更新箱子的开箱次数等统计信息
      if (data.open_count !== undefined) {
        const currentDetail = caseStore.getCaseDetail(caseKey)
        if (currentDetail) {
          caseStore.updateCaseDetail(caseKey, {
            ...currentDetail,
            open_count: data.open_count
          })
        }
      }
    }
  } catch (error) {
    console.error('[Cases详情页] 处理箱子统计更新失败:', error)
  }
}

// 初始化Socket监听器 - 增强版本
const initializeSocket = () => {
  // 设置store的Socket监听器
  caseStore.setupSocketListeners()

  // 加入开箱记录房间
  socketRoomManager.joinRoom(socketRooms.caseRecords(caseKey))

  // 监听开箱记录更新
  socketRoomManager.addEventListener(socketEvents.caseRecords.update, handleCaseRecordsUpdate)
  socketRoomManager.addEventListener(socketEvents.caseRecords.stats, handleCaseStatsUpdate)

  // 监听通用的开箱记录事件（兼容性）
  window.addEventListener('case-records-updated', (event: Event) => {
    const customEvent = event as CustomEvent
    const { caseKey: eventCaseKey, source } = customEvent.detail || {}

    if (eventCaseKey === caseKey && source === 'socket') {
      console.log(`[Cases详情页] 收到开箱记录更新事件: ${eventCaseKey}`)
      // 可以在这里添加额外的UI更新逻辑
    }
  })

  console.log(`[Cases详情页] Socket监听器已设置，监听箱子: ${caseKey}`)
}

// 页面挂载时获取数据
onMounted(async () => {
  try {
    isLoading.value = true
    
    // 检查用户认证状态
    if (!userStore.isAuthenticated) {
      await userStore.initUserState()
    }
    
    // 初始化Socket监听器
    initializeSocket()
    
    // 使用store的初始化方法并行获取数据
    await caseStore.initializeCaseData(caseKey)
    
  } catch (err) {
    console.error('页面数据加载失败:', err)
    error.value = err instanceof Error ? err.message : '页面加载失败'
  } finally {
    isLoading.value = false
  }
})

// 监听语言变化
watch(locale, () => {
  updateSEO()
})

// 监听箱子数据变化，更新SEO
watch(caseDetail, () => {
  if (caseDetail.value.name) {
    updateSEO()
  }
}, { deep: true })

// 页面卸载时清理
onUnmounted(() => {
  // 清理Socket房间订阅
  socketRoomManager.cleanup()
  console.log(`[Cases详情页] 已清理Socket订阅 - ${caseKey}`)
})
</script>

<style lang="scss" scoped>
/* 过渡动画 */
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

/* 网格背景图案 */
.bg-grid-pattern {
  background-image: 
    linear-gradient(rgba(255, 255, 255, 0.03) 1px, transparent 1px),
    linear-gradient(90deg, rgba(255, 255, 255, 0.03) 1px, transparent 1px);
  background-size: 20px 20px;
}

/* 文本截断 */
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* 硬件加速优化 */
.group {
  transform: translateZ(0);
  will-change: transform;
}

/* 响应式优化 */
@media (max-width: 768px) {
  .grid {
    gap: 0.75rem;
  }
  
  .p-6 {
    padding: 1rem;
  }
  
  .text-xl {
    font-size: 1.125rem;
  }
}

/* 自定义滚动条 */
.overflow-x-auto::-webkit-scrollbar {
  height: 4px;
}

.overflow-x-auto::-webkit-scrollbar-track {
  background: rgba(55, 65, 81, 0.3);
  border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb {
  background: var(--color-primary);
  border-radius: 2px;
}

.overflow-x-auto::-webkit-scrollbar-thumb:hover {
  background: var(--color-primary-dark);
}
</style>