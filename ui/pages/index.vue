// pages/index.vue
<template>
  <div class="min-h-screen relative">
    <!-- 增强背景层 -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- 动态渐变背景 - 增加深度 -->
      <div class="absolute inset-0 bg-gradient-to-b from-background-darker via-background to-background-dark z-0" />
      <!-- 粒子效果层 - 保留原有效果 -->
      <div class="particle-container absolute inset-0 z-0" />
      <!-- 增强动态光线效果 -->
      <div class="absolute top-0 left-0 w-full h-full overflow-hidden opacity-15 z-0">
        <div class="light-beam light-beam-1" />
        <div class="light-beam light-beam-3" />
        <div class="light-beam light-beam-2" />
      </div>
      <!-- 增强网格线效果 - 更精细 -->
      <div class="absolute inset-0 bg-grid-pattern opacity-[0.04] z-0" />
    </div>

    <!-- PC版内容 -->
    <div class="container mx-auto px-4 py-6 relative z-10" v-if="!isMobile">
      <keep-alive>
        <PCHomeContent
          :hero-slides="bannerData || homeStore.banner"
          :hot-cases="hotCasesData || homeStore.hotCases"
          :discount-cases="discountCasesData || homeStore.discountCases"
          :new-cases="newCasesData || homeStore.newCases"
          :random-skins="randomSkinsData || homeStore.randomSkins"
          @refresh-random-skins="refreshRandomSkins"
        />
      </keep-alive>
    </div>

    <!-- 移动端内容 -->
    <div class="container mx-auto px-3 py-4 relative z-10" v-else>
      <keep-alive>
        <MobileHomeContent
          :hero-slides="bannerData || homeStore.banner"
          :hot-cases="hotCasesData || homeStore.hotCases"
          :random-skins="randomSkinsData || homeStore.randomSkins"
          @refresh-random-skins="refreshRandomSkins"
        />
      </keep-alive>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted, watch } from 'vue'
import { useAppStore } from '~/stores/app'
import PCHomeContent from '~/components/home/<USER>'
import MobileHomeContent from '~/components/home/<USER>'
import { useLanguageStore } from '~/stores/language'
import { useSocketStore } from '~/stores/socket'
import { useHomeStore } from '~/stores/home'
import { useCaseStore } from '~/stores/case'
import { siteApi } from '~/services/site-api'
import { caseApi } from '~/services/case-api'
import { skinApi } from '~/services/skin-api'
import { monitorApi } from '~/services/monitor-api'

// SEO配置
const { $i18n } = useNuxtApp()
useHead({
  title: () => $i18n.t('home.title'),
  meta: [
    {
      name: 'description',
      content: () => $i18n.t('home.description')
    },
    {
      name: 'keywords',
      content: 'CSGO, CS:GO, cases, skins, gaming, weapons, 开箱, 皮肤'
    },
    {
      property: 'og:title',
      content: () => $i18n.t('home.title')
    },
    {
      property: 'og:description',
      content: () => $i18n.t('home.description')
    }
  ]
})

// Stores
const langStore = useLanguageStore()
const socketStore = useSocketStore()
const homeStore = useHomeStore()
const caseStore = useCaseStore()

// 确保messages对象初始化
if (!langStore.messages) {
  langStore.messages = { en: {}, zh_hans: {} }
}

// 获取设备状态
const store = useAppStore()
const isMobile = computed(() => store.isMobile)

// SSR数据获取 - 这些数据会在服务端预取，对SEO友好
const { data: bannerData, refresh: refreshBanner } = useFetch('/api/sitecfg/banner/', {
  key: 'home-banner',
  transform: (response: any) => {
    if (response.code === 0 && response.body?.items) {
      return homeStore.processBannerData(response.body.items, langStore.currentLanguage)
    }
    return []
  },
  default: () => [],
  server: false, // 仅客户端渲染，保留骨架屏显示
  lazy: true    // 路由先渲染骨架，挂载后再拉取
})

const { data: hotCasesData, refresh: refreshHotCases } = useFetch('/api/box/tag/', {
  key: 'home-hot-cases',
  query: { q: 'HOT', num: 5 },
  transform: (response: any) => {
    if (response.code === 0 && response.body?.items) {
      return response.body.items
    }
    return []
  },
  default: () => [],
  server: false,
  lazy: true
})

const { data: newCasesData, refresh: refreshNewCases } = useFetch('/api/box/tag/', {
  key: 'home-new-cases',
  query: { q: 'new', num: 5 },
  transform: (response: any) => {
    if (response.code === 0 && response.body?.items) {
      return response.body.items
    }
    return []
  },
  default: () => [],
  server: false,
  lazy: true
})

const { data: discountCasesData, refresh: refreshDiscountCases } = useFetch('/api/box/tag/', {
  key: 'home-discount-cases',
  query: { q: 'SELL', num: 5 },
  transform: (response: any) => {
    if (response.code === 0 && response.body?.items) {
      return response.body.items
    }
    return []
  },
  default: () => [],
  server: false,
  lazy: true
})

const { data: randomSkinsData, refresh: refreshRandomSkinsData } = useFetch('/api/package/items/random', {
  key: 'home-random-skins',
  query: { 
    num: 12,
    excludeTypes: 'CSGO_Type_Spray,CSGO_Tool_Sticker',
    domain: 'www.csgo.com'
  },
  transform: (response: any) => {
    if (response.code === 0 && response.body?.items) {
      return response.body.items
    }
    return []
  },
  default: () => [],
  server: false,
  lazy: true
})

// 统计数据和开箱记录 - 混合渲染，确保LiveOpenings有初始数据
const { data: statsData } = useFetch('/api/monitor/data/', {
  key: 'home-stats',
  transform: (response: any) => {
    console.log('[首页] /api/monitor/data/ 响应:', response)
    if (response && response.code === 0 && response.body) {
      console.log('[首页] 统计数据获取成功:', {
        stats: !!response.body.stats,
        case_records: response.body.case_records?.length || 0
      })
      return response.body
    }
    console.warn('[首页] 统计数据获取失败或为空:', response)
    return null
  },
  default: () => null,
  server: true,  // 服务端预取，确保LiveOpenings有初始数据
  lazy: true,    // 页面先渲染，挂载后异步获取
  onRequestError({ error }) {
    console.error('[首页] /api/monitor/data/ 请求错误:', error)
  },
  onResponseError({ response }) {
    console.error('[首页] /api/monitor/data/ 响应错误:', response.status, response.statusText)
  }
})

// 刷新随机皮肤
const refreshRandomSkins = async () => {
  await refreshRandomSkinsData()
  // 同时更新store中的数据
  if (randomSkinsData.value) {
    homeStore.updateRandomSkins(randomSkinsData.value)
  }
}

// 初始化Socket数据和实时更新
const initializeSocketData = async () => {
  try {
    console.log('[首页] 开始初始化Socket数据...')
    console.log('[首页] statsData.value:', statsData.value)

    // 如果有统计数据，先设置到store
    if (statsData.value) {
      console.log('[首页] 设置统计数据到store...')
      if (statsData.value.stats) {
        socketStore.setStatsData(statsData.value.stats)
        console.log('[首页] 统计数据已设置')
      }
      if (statsData.value.case_records) {
        console.log('[首页] 设置开箱记录到store，数量:', statsData.value.case_records.length)
        socketStore.setCaseRecords(statsData.value.case_records)
        console.log('[首页] 开箱记录已设置')
      } else {
        console.warn('[首页] 没有开箱记录数据')
      }
    } else {
      console.warn('[首页] 没有统计数据，等待API数据或Socket连接...')
      // 不使用模拟数据，等待真实API数据
    }

    // 等待Socket连接建立
    let waitTime = 0
    while (!socketStore.isConnected && waitTime < 3000) {
      await new Promise(resolve => setTimeout(resolve, 100))
      waitTime += 100
    }

    // 如果Socket连接成功，请求最新数据进行更新
    if (socketStore.isConnected) {
      console.log('[首页] Socket已连接，请求最新数据...')
      socketStore.requestCaseRecords()
      socketStore.requestStatsData()
    } else {
      console.warn('[首页] Socket连接超时，将仅使用API数据')
    }

  } catch (error) {
    console.error('[首页] Socket数据初始化失败:', error)
  }
}

// 同步数据到store（保持向后兼容性）
const syncDataToStores = () => {
  // 同步banner数据到homeStore
  if (bannerData.value && bannerData.value.length > 0) {
    homeStore.setBannerData(bannerData.value)
  }
  
  // 同步箱子数据到caseStore
  if (hotCasesData.value && hotCasesData.value.length > 0) {
    caseStore.setHotCases(hotCasesData.value)
  }
  
  if (newCasesData.value && newCasesData.value.length > 0) {
    caseStore.setNewCases(newCasesData.value)
  }
  
  if (discountCasesData.value && discountCasesData.value.length > 0) {
    caseStore.setDiscountCases(discountCasesData.value)
  }
  
  // 同步皮肤数据到homeStore
  if (randomSkinsData.value && randomSkinsData.value.length > 0) {
    homeStore.updateRandomSkins(randomSkinsData.value)
  }
}

// 处理语言变化
const handleLanguageChange = async () => {
  // 重新获取banner数据以应用新的语言
  await refreshBanner()
}

// 处理页面可见性变化
const handleVisibilityChange = () => {
  if (!document.hidden && socketStore.isConnected) {
    // 页面重新可见时，请求最新数据
    socketStore.requestCaseRecords()
    socketStore.requestStatsData()
  }
}

// 处理刷新开箱记录
const handleRefreshOpenings = () => {
  if (socketStore.isConnected) {
    socketStore.requestCaseRecords()
  }
}

// 初始化粒子效果
const initParticles = () => {
  if (!document.querySelector('.particle-container')) return
  
  const container = document.querySelector('.particle-container')
  if (!container) return
  
  // 创建粒子
  for (let i = 0; i < 50; i++) {
    const particle = document.createElement('div')
    particle.className = 'particle'
    particle.style.cssText = `
      position: absolute;
      width: 2px;
      height: 2px;
      background: rgba(59, 130, 246, 0.6);
      border-radius: 50%;
      pointer-events: none;
      left: ${Math.random() * 100}%;
      top: ${Math.random() * 100}%;
      animation: float ${3 + Math.random() * 4}s ease-in-out infinite;
      animation-delay: ${Math.random() * 2}s;
    `
    container.appendChild(particle)
  }
}

// 页面挂载时的初始化
onMounted(async () => {
  // 同步SSR数据到stores
  syncDataToStores()

  // 初始化Socket数据和实时更新
  await initializeSocketData()

  if (process.client) {
    // 添加事件监听器
    window.addEventListener('languageChanged', handleLanguageChange)
    window.addEventListener('refreshOpenings', handleRefreshOpenings)
    document.addEventListener('visibilitychange', handleVisibilityChange)

    // 初始化粒子效果
    initParticles()
  }
})

// 页面卸载时清理
onUnmounted(() => {
  if (process.client) {
    window.removeEventListener('languageChanged', handleLanguageChange)
    window.removeEventListener('refreshOpenings', handleRefreshOpenings)
    document.removeEventListener('visibilitychange', handleVisibilityChange)
  }
})

// 监听客户端数据加载完成后再次同步到 Store，确保骨架屏结束后立即更新界面
watch(
  [bannerData, hotCasesData, newCasesData, discountCasesData, randomSkinsData],
  () => {
    syncDataToStores()
  },
  { immediate: true }
)
</script>

<style scoped>
/* 粒子动画 */
@keyframes float {
  0%, 100% {
    transform: translateY(0px) rotate(0deg);
    opacity: 0.6;
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
    opacity: 1;
  }
}

/* 光线效果 */
.light-beam {
  position: absolute;
  background: linear-gradient(45deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  animation: beam 8s ease-in-out infinite;
}

.light-beam-1 {
  width: 2px;
  height: 100%;
  left: 20%;
  animation-delay: 0s;
}

.light-beam-2 {
  width: 1px;
  height: 100%;
  left: 60%;
  animation-delay: 2s;
}

.light-beam-3 {
  width: 3px;
  height: 100%;
  left: 80%;
  animation-delay: 4s;
}

@keyframes beam {
  0%, 100% {
    opacity: 0;
    transform: translateX(-50px);
  }
  50% {
    opacity: 1;
    transform: translateX(50px);
  }
}

/* 网格背景 */
.bg-grid-pattern {
  background-image: 
    linear-gradient(rgba(59, 130, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(59, 130, 246, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
}
</style>