import { defineNuxtPlugin } from '#app'

// 🚀 根据WebSocket API文档重新实现的Socket插件
// 文档规定：所有消息格式为 [messageType, action, data, socketId?]
// 所有消息通过 ws_channel 频道发送

export default defineNuxtPlugin(async () => {
  if (process.server) return

  const config = useRuntimeConfig()
  const socketStore = useSocketStore()

  try {
    // 动态导入Socket.IO客户端
    const socketIO = await import('socket.io-client')
    const io = socketIO.default

    // 开发环境使用本地代理，生产环境使用服务器地址
    const isDev = process.env.NODE_ENV === 'development'
    const socketUrl = isDev
      ? window.location.origin  // 开发环境使用本地代理
      : (config.public.socketUrl || 'https://socket.cs2.net.cn')
    // 创建Socket.IO连接
    const socket = io(socketUrl, {
      transports: ['polling', 'websocket'],
      forceNew: false,
      reconnection: true,
      reconnectionAttempts: 5,
      reconnectionDelay: 1000,
      reconnectionDelayMax: 5000,
      timeout: 10000,
      autoConnect: true,
      path: '/socket.io/',
      query: {
        page: 'ws',
        EIO: '4'
      }
    })
    
    // Socket.IO连接成功
    socket.on('connect', () => {
      socketStore.setConnected(true)
      socketStore.setSocketId(socket.id)
      socketStore.setSocket(socket)

      // 加入监听频道和请求初始数据
      socket.emit('join', 'ws_channel')

      // 使用新的消息格式
      socket.emit('monitor', ['join', 'monitor'])
      socket.emit('monitor', ['get_stats'])
      socket.emit('monitor', ['case_records'])

      // 派发全局连接事件
      window.dispatchEvent(new CustomEvent('socket:connected', {
        detail: { socketId: socket.id }
      }))
    })

    // Socket.IO连接断开
    socket.on('disconnect', (reason: string) => {
      socketStore.setConnected(false)
      socketStore.setSocketId(null)

      window.dispatchEvent(new CustomEvent('socket:disconnected', {
        detail: { reason }
      }))
    })

    // Socket.IO连接错误
    socket.on('connect_error', (error: any) => {
      console.error('Socket.IO连接错误:', error)
      socketStore.setConnectionError(error.message)
    })
    
    // 🎯 监听ws_channel频道的数组格式消息
    socket.on('ws_channel', (message: any) => {
      // 验证消息格式：必须是数组格式 [messageType, action, data, socketId?]
      if (!Array.isArray(message) || message.length < 3) {
        return
      }

      const [messageType, action, data, socketId] = message

      // 验证messageType和action为字符串
      if (typeof messageType !== 'string' || typeof action !== 'string') {
        return
      }

      // 根据messageType分发消息处理
      handleWebSocketMessage(messageType, action, data, socketId)
    })

    // 🎯 修复：监听message事件（实际的消息通道）
    socket.on('message', (message: any) => {
      // 验证消息格式：必须是数组格式 [messageType, action, data, socketId?]
      if (!Array.isArray(message) || message.length < 3) {
        return
      }

      const [messageType, action, data, socketId] = message

      // 验证messageType和action为字符串
      if (typeof messageType !== 'string' || typeof action !== 'string') {
        return
      }

      // 根据messageType分发消息处理
      handleWebSocketMessage(messageType, action, data, socketId)
    })

    // 🔍 调试：监听其他可能的事件（仅开发环境）
    if (process.env.NODE_ENV === 'development') {
      const debugEvents = ['data', 'update', 'monitor', 'case-records', 'box', 'stats', 'opening', 'openings', 'boxroom', 'boxroomdetail']
      debugEvents.forEach(eventName => {
        socket.on(eventName, (data: any) => {
          console.log(`Socket事件 [${eventName}]:`, data)
        })
      })
    }
    
    // Socket对象已经在connect事件中设置到store了
    
    // 🔧 处理WebSocket消息的核心函数
    function handleWebSocketMessage(messageType: string, action: string, data: any, socketId?: string) {
      try {
        // 更新统计信息
        socketStore.statistics.messagesReceived++
        socketStore.lastMessage = new Date()
        
        switch (messageType) {
          case 'boxroom':
            // 房间级别状态变化
            handleBoxroomMessage(action, data)
            break
            
          case 'boxroomdetail':
            // 房间详情变化 (动画同步消息)
            handleBoxroomDetailMessage(action, data, socketId)
            break
            
          case 'box':
            // 个人开箱消息
            handleBoxMessage(action, data)
            break
            
          case 'monitor':
            // 监控系统消息
            handleMonitorMessage(action, data)
            break
            
          case 'case_records':
            // 开箱记录消息
            handleCaseRecordsMessage(action, data)
            break
            
          case 'online_number':
            // 在线人数更新
            handleOnlineNumberMessage(action, data)
            break
            
          default:
            if (process.env.NODE_ENV === 'development') {
              console.warn(`未知消息类型: ${messageType}`, { action, data })
            }
        }

        // 派发通用的socket消息事件
        window.dispatchEvent(new CustomEvent('socket:message', {
          detail: {
            messageType,
            action,
            data,
            socketId,
            timestamp: Date.now()
          }
        }))

      } catch (error) {
        console.error(`处理Socket消息失败 [${messageType}, ${action}]:`, error)
        socketStore.statistics.errors++
      }
    }
    
    // 🏠 处理房间级别消息 (boxroom)
    function handleBoxroomMessage(action: string, data: any) {
      
      switch (action) {
        case 'new':
          // 新房间创建
          window.dispatchEvent(new CustomEvent('socket:room_created', { detail: data }))
          break
          
        case 'update':
          // 房间状态更新 (包括玩家加入/离开)
          window.dispatchEvent(new CustomEvent('socket:room_updated', { detail: data }))
          break
          
        case 'start':
          // 对战开始
          window.dispatchEvent(new CustomEvent('socket:battle_started', { detail: data }))
          break
          
        case 'cancel':
          // 房间取消
          window.dispatchEvent(new CustomEvent('socket:room_cancelled', { detail: data }))
          break
          
        default:
          if (process.env.NODE_ENV === 'development') {
            console.warn(`未知房间操作: ${action}`, data)
          }
      }
      
      // 同步到Store (保持向后兼容)
      socketStore.handleBattleMessage(action, data)
    }
    
    // 🎮 处理房间详情消息 (boxroomdetail) - 动画同步核心
    function handleBoxroomDetailMessage(action: string, data: any, socketId?: string) {
      
      switch (action) {
        case 'round_start':
          // 回合开始 - 支持时间戳同步
          window.dispatchEvent(new CustomEvent('socket:round_start', { 
            detail: { data, socketId, timestamp: Date.now() } 
          }))
          break
          
        case 'opening_start':
          // 开箱动画触发 - 支持时间戳同步
          window.dispatchEvent(new CustomEvent('socket:opening_start', { 
            detail: { data, socketId, timestamp: Date.now() } 
          }))
          break
          
        case 'animation_progress':
          // 动画进度同步
          window.dispatchEvent(new CustomEvent('socket:animation_progress', { 
            detail: { data, socketId, timestamp: Date.now() } 
          }))
          break
          
        case 'round_result':
          // 回合结果
          window.dispatchEvent(new CustomEvent('socket:round_result', { 
            detail: { data, socketId, timestamp: Date.now() } 
          }))
          break
          
        case 'battle_end':
          // 对战结束
          window.dispatchEvent(new CustomEvent('socket:battle_end', { 
            detail: { data, socketId, timestamp: Date.now() } 
          }))
          break
          
        case 'time_sync_request':
          // 时钟同步请求 (新增功能)
          window.dispatchEvent(new CustomEvent('socket:time_sync_request', { 
            detail: { data, socketId, timestamp: Date.now() } 
          }))
          break
          
        // 兼容性消息 (保持向后兼容)
        case 'round':
        case 'end':
          window.dispatchEvent(new CustomEvent(`socket:legacy_${action}`, {
            detail: { data, socketId }
          }))
          break

        default:
          if (process.env.NODE_ENV === 'development') {
            console.warn(`未知房间详情操作: ${action}`, data)
          }
      }
      
      // 同步到Store (保持向后兼容)
      socketStore.handleBattleDetailMessage(action, data, socketId)
    }
    
    // 📦 处理个人开箱消息 (box)
    function handleBoxMessage(action: string, data: any) {

      switch (action) {
        case 'new':
          // 新开箱结果 (延迟15秒发送)
          // 派发正确的事件名称，与useLiveOpenings.ts中的监听器匹配
          window.dispatchEvent(new CustomEvent('socket-case-opened', { detail: { data: data } }))
          // 同时派发旧格式以保持兼容性
          window.dispatchEvent(new CustomEvent('socket:case_opened', { detail: data }))
          break

        case 'details':
          // 箱子详情更新 (已注释功能)
          window.dispatchEvent(new CustomEvent('socket:case_details', { detail: data }))
          break

        default:
          if (process.env.NODE_ENV === 'development') {
            console.warn(`未知开箱操作: ${action}`, data)
          }
      }
    }
    
    // 📊 处理监控系统消息 (monitor)
    function handleMonitorMessage(action: string, data: any) {

      switch (action) {
        case 'update':
          // 监控统计数据更新
          socketStore.updateMonitorData(data)
          // 派发正确的事件名称，与useLiveOpenings.ts中的监听器匹配
          window.dispatchEvent(new CustomEvent('socket-monitor', { detail: { data: data } }))
          // 同时派发旧格式以保持兼容性
          window.dispatchEvent(new CustomEvent('socket:monitor_update', { detail: data }))
          break

        default:
          if (process.env.NODE_ENV === 'development') {
            console.warn(`未知监控操作: ${action}`, data)
          }
      }
    }
    
    // 📝 处理开箱记录消息 (case_records)
    function handleCaseRecordsMessage(action: string, data: any) {

      switch (action) {
        case 'update':
          // 开箱记录更新
          if (Array.isArray(data)) {
            socketStore.updateCaseRecords(data)
            // 派发正确的事件名称，与useLiveOpenings.ts中的监听器匹配
            window.dispatchEvent(new CustomEvent('socket-case-records', { detail: { data: data } }))
            // 同时派发旧格式以保持兼容性
            window.dispatchEvent(new CustomEvent('socket:case_records_update', { detail: data }))
          }
          break

        default:
          if (process.env.NODE_ENV === 'development') {
            console.warn(`未知开箱记录操作: ${action}`, data)
          }
      }
    }
    
    // 👥 处理在线人数消息 (online_number)
    function handleOnlineNumberMessage(action: string, data: any) {
      
      switch (action) {
        case 'update':
          // 在线人数更新
          if (typeof data === 'number') {
            socketStore.updateStatsData({ online_number: data })
            window.dispatchEvent(new CustomEvent('socket:online_number_update', { detail: data }))
          }
          break

        default:
          if (process.env.NODE_ENV === 'development') {
            console.warn(`未知在线人数操作: ${action}`, data)
          }
      }
    }
    
    // 清理函数
    window.addEventListener('beforeunload', () => {
      if (socket) {
        socket.disconnect()
      }
    })

  } catch (error) {
    console.error('Socket.IO初始化失败:', error)
    socketStore.setConnectionError('Socket.IO初始化失败')
  }
})