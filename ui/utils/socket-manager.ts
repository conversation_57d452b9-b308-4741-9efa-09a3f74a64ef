/**
 * WebSocket房间管理工具
 * 用于管理不同页面的Socket房间订阅
 */

import { useSocketStore } from '~/stores/socket'

export interface SocketRoomConfig {
  room: string
  action: string
  data?: any
}

export class SocketRoomManager {
  private socketStore: any
  private subscribedRooms: Set<string> = new Set()
  private eventListeners: Map<string, Function[]> = new Map()

  constructor() {
    if (process.client) {
      this.socketStore = useSocketStore()
    }
  }

  /**
   * 加入房间
   */
  async joinRoom(config: SocketRoomConfig): Promise<boolean> {
    if (!process.client || !this.socketStore) {
      return false
    }

    try {
      const roomKey = `${config.room}:${config.action}`
      
      if (this.subscribedRooms.has(roomKey)) {
        console.log(`[SocketRoomManager] 房间已订阅: ${roomKey}`)
        return true
      }

      // 发送加入房间消息
      const message = {
        room: config.room,
        action: config.action,
        data: config.data || {}
      }

      const success = await this.socketStore.sendMessage(message)
      
      if (success) {
        this.subscribedRooms.add(roomKey)
        console.log(`[SocketRoomManager] 成功加入房间: ${roomKey}`)
        return true
      } else {
        console.error(`[SocketRoomManager] 加入房间失败: ${roomKey}`)
        return false
      }
    } catch (error) {
      console.error('[SocketRoomManager] 加入房间异常:', error)
      return false
    }
  }

  /**
   * 离开房间
   */
  async leaveRoom(config: SocketRoomConfig): Promise<boolean> {
    if (!process.client || !this.socketStore) {
      return false
    }

    try {
      const roomKey = `${config.room}:${config.action}`
      
      if (!this.subscribedRooms.has(roomKey)) {
        console.log(`[SocketRoomManager] 房间未订阅: ${roomKey}`)
        return true
      }

      // 发送离开房间消息
      const message = {
        room: config.room,
        action: 'leave',
        data: config.data || {}
      }

      await this.socketStore.sendMessage(message)
      this.subscribedRooms.delete(roomKey)
      
      // 清理事件监听器
      this.removeEventListeners(roomKey)
      
      console.log(`[SocketRoomManager] 离开房间: ${roomKey}`)
      return true
    } catch (error) {
      console.error('[SocketRoomManager] 离开房间异常:', error)
      return false
    }
  }

  /**
   * 添加事件监听器
   */
  addEventListener(eventName: string, callback: Function): void {
    if (!process.client) return

    if (!this.eventListeners.has(eventName)) {
      this.eventListeners.set(eventName, [])
    }
    
    this.eventListeners.get(eventName)?.push(callback)
    window.addEventListener(eventName, callback as EventListener)
  }

  /**
   * 移除事件监听器
   */
  removeEventListener(eventName: string, callback: Function): void {
    if (!process.client) return

    const listeners = this.eventListeners.get(eventName)
    if (listeners) {
      const index = listeners.indexOf(callback)
      if (index > -1) {
        listeners.splice(index, 1)
      }
    }
    
    window.removeEventListener(eventName, callback as EventListener)
  }

  /**
   * 移除指定房间的所有事件监听器
   */
  private removeEventListeners(roomKey: string): void {
    const eventNames = Array.from(this.eventListeners.keys()).filter(name => 
      name.includes(roomKey.split(':')[0])
    )
    
    eventNames.forEach(eventName => {
      const listeners = this.eventListeners.get(eventName) || []
      listeners.forEach(callback => {
        window.removeEventListener(eventName, callback as EventListener)
      })
      this.eventListeners.delete(eventName)
    })
  }

  /**
   * 清理所有订阅
   */
  cleanup(): void {
    if (!process.client) return

    // 离开所有房间
    this.subscribedRooms.forEach(roomKey => {
      const [room, action] = roomKey.split(':')
      this.leaveRoom({ room, action })
    })

    // 清理所有事件监听器
    this.eventListeners.forEach((listeners, eventName) => {
      listeners.forEach(callback => {
        window.removeEventListener(eventName, callback as EventListener)
      })
    })
    
    this.subscribedRooms.clear()
    this.eventListeners.clear()
  }

  /**
   * 获取已订阅的房间列表
   */
  getSubscribedRooms(): string[] {
    return Array.from(this.subscribedRooms)
  }
}

// 全局实例
let socketRoomManager: SocketRoomManager | null = null

export function useSocketRoomManager(): SocketRoomManager {
  if (!socketRoomManager) {
    socketRoomManager = new SocketRoomManager()
  }
  return socketRoomManager
}

// 便捷方法 - 增强版本
export const socketRooms = {
  // 监控房间 - 用于首页统计
  monitor: {
    room: 'monitor',
    action: 'join'
  },

  // 开箱记录房间 - 用于开箱详情页
  caseRecords: (caseKey: string) => ({
    room: 'case_records',
    action: 'join',
    data: { case_key: caseKey }
  }),

  // 对战房间 - 用于对战详情页
  battle: (battleId: string) => ({
    room: 'boxroomdetail',
    action: 'join',
    data: { room_id: battleId }
  }),

  // 对战列表房间 - 用于对战列表页
  battleList: {
    room: 'boxroom',
    action: 'join'
  }
}

// Socket事件配置 - 统一管理所有Socket事件
export const socketEvents = {
  // 监控相关事件
  monitor: {
    update: 'socket:monitor:update',
    stats: 'socket:monitor:stats',
    userCount: 'socket:monitor:user_count',
    caseCount: 'socket:monitor:case_count'
  },

  // 开箱记录相关事件
  caseRecords: {
    update: 'socket:case_records:update',
    new: 'socket:case_records:new',
    stats: 'socket:case_records:stats'
  },

  // 对战相关事件
  battle: {
    update: 'socket:battle:update',
    start: 'socket:battle:start',
    end: 'socket:battle:end',
    roundUpdate: 'socket:battle:round_update',
    playerJoin: 'socket:battle:player_join',
    playerLeave: 'socket:battle:player_leave'
  },

  // 连接状态事件
  connection: {
    connected: 'socket:connected',
    disconnected: 'socket:disconnected',
    reconnecting: 'socket:reconnecting',
    error: 'socket:error'
  }
}

// 事件名称常量
export const socketEvents = {
  // 监控事件
  monitor: {
    update: 'socket:monitor:update',
    stats: 'socket:monitor:stats'
  },
  
  // 开箱记录事件
  caseRecords: {
    update: 'socket:case_records:update'
  },
  
  // 对战事件
  battle: {
    update: 'socket:battle:update',
    start: 'socket:battle:start',
    end: 'socket:battle:end',
    cancel: 'socket:battle:cancel',
    join: 'socket:battle:join',
    leave: 'socket:battle:leave'
  }
}
