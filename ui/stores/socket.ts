import { defineStore } from 'pinia'
import type { CaseRecord } from '~/services/monitor-api'
import { useAnimationSync } from '~/composables/useAnimationSync'

interface User {
  id: number
  name: string
  avatar?: string
}

interface Item {
  id: number
  name: string
  rarity?: string
  image?: string
}

interface StatsData {
  user_number: number
  online_number: number
  case_number: number
  battle_number: number
  totalOpenings?: number
  totalUsers?: number
  totalCases?: number
  totalSkins?: number
  recentOpenings?: CaseRecord[]
}

interface SocketState {
  isConnected: boolean
  socketId: string | null
  monitorData: any
  caseRecords: CaseRecord[]
  socket: any
  statsData: StatsData
  connectionError: string | null
  reconnectAttempts: number
  maxReconnectAttempts: number
  offlineMode: boolean
  lastMsgTime: number
  statistics: {
    messagesReceived: number
    errors: number
  }
  lastMessage: Date | null
  _lastSyntheticOpening?: number
}

export const useSocketStore = defineStore('socket', {
  state: (): SocketState => ({
    isConnected: false,
    socketId: null,
    monitorData: null,
    caseRecords: [],
    socket: null,
    statsData: {
      user_number: 0,
      online_number: 0,
      case_number: 0,
      battle_number: 0,
      totalOpenings: 0,
      totalUsers: 0,
      totalCases: 0,
      totalSkins: 0,
      recentOpenings: []
    },
    connectionError: null,
    reconnectAttempts: 0,
    maxReconnectAttempts: 5,
    offlineMode: false,
    lastMsgTime: 0,
    statistics: {
      messagesReceived: 0,
      errors: 0
    },
    lastMessage: null,
    _lastSyntheticOpening: undefined
  }),
  
  actions: {
    setConnected(status: boolean) {
      this.isConnected = status
      this.offlineMode = !status
      if (status) {
        this.connectionError = null
        this.reconnectAttempts = 0
      }
    },
    
    setSocketId(id: string | null) {
      this.socketId = id
    },
    
    setConnectionError(error: string | null) {
      this.connectionError = error
    },

    setSocket(socket: any) {
      this.socket = socket
      
      if (socket) {
        // 当设置新的socket实例时，自动设置消息监听器
        this.setupMessageListener(socket)
      }
    },
    
    incrementReconnectAttempts() {
      this.reconnectAttempts++
    },
    
    resetReconnectAttempts() {
      this.reconnectAttempts = 0
    },
    
    updateMonitorData(data: any) {
      this.monitorData = data
      
      // 检查monitor数据中是否包含统计信息
      if (data && typeof data === 'object') {
        // 检查是否有统计数据字段
        if (data.stats) {
          this.setStatsData(data.stats)
        } else if (data.user_number !== undefined || data.case_number !== undefined || 
                   data.online_number !== undefined || data.battle_number !== undefined) {
          this.setStatsData(data)
        }
      }
    },
    
    updateCaseRecords(data: CaseRecord[]) {
      this.caseRecords = data
      
      // 派发事件给CaseStore
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('socket-case-records', {
          detail: { data }
        }))
      }
    },

    setCaseRecords(records: CaseRecord[]) {
      // 确保数据完整性，过滤掉不完整的记录
      if (!records || !Array.isArray(records)) {
        console.warn('[Socket Store] setCaseRecords: Invalid records data', records)
        return
      }

      // 过滤掉不完整的记录
      const validRecords = records.filter(record => {
        // 确保必要的嵌套对象存在
        if (!record || !record.item_info) {
          return false
        }

        // 确保 item_rarity 存在且有 rarity_color
        if (!record.item_info.item_rarity || !record.item_info.item_rarity.rarity_color) {
          // 尝试修复数据
          if (record.item_info.item_rarity && !record.item_info.item_rarity.rarity_color) {
            record.item_info.item_rarity.rarity_color = '#ffffff' // 设置默认颜色
          } else if (!record.item_info.item_rarity) {
            // 创建默认的 item_rarity 对象
            record.item_info.item_rarity = {
              rarity_id: 1,
              rarity_name: 'common',
              rarity_name_en: 'common',
              rarity_name_zh_hans: '普通',
              rarity_color: '#ffffff'
            }
          }
        }

        return true
      })

      this.caseRecords = validRecords
    },
    
    clearCaseRecords() {
      this.caseRecords = []
    },

    setStatsData(stats: StatsData) {
      this.statsData = { ...this.statsData, ...stats }
      // console.log('[SocketStore] 统计数据已更新:', this.statsData) // 调试时可启用
      
      // 派发统计数据更新事件
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('stats-data-updated', { 
          detail: { stats: this.statsData, timestamp: Date.now() } 
        }))
      }
    },

    updateStatsData(updates: Partial<StatsData>) {
      this.statsData = { ...this.statsData, ...updates }
      // console.log('[SocketStore] 统计数据增量更新:', updates, '最终数据:', this.statsData) // 调试时可启用
      
      // 派发统计数据更新事件
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('stats-data-updated', { 
          detail: { stats: this.statsData, updates, timestamp: Date.now() } 
        }))
      }
    },

    async reconnect() {
      if (this.socket) {
        try {
          this.socket.disconnect()
          this.socket.connect()
          console.log('[SocketStore] 正在重连Socket...')
        } catch (error) {
          console.error('[SocketStore] 重连失败:', error)
          this.setConnectionError('重连失败')
        }
      }
    },

    disconnect() {
      if (this.socket) {
        this.socket.disconnect()
        this.setConnected(false)
        this.setSocketId(null)
      }
    },

    emit(event: string, data?: any) {
      if (this.socket && this.isConnected) {
        this.socket.emit(event, data)
        // console.log(`[SocketStore] 发送事件: ${event}`, data) // 调试时可启用
      } else {
        console.warn(`[SocketStore] Socket未连接，无法发送事件: ${event}`)
      }
    },

    // 发送消息 - 支持新的房间管理器
    async sendMessage(message: any): Promise<boolean> {
      try {
        if (!this.socket || !this.isConnected) {
          console.warn('[SocketStore] Socket未连接，无法发送消息')
          return false
        }

        // 根据消息类型发送不同格式的消息
        if (message.room && message.action) {
          // 房间管理消息
          if (message.room === 'monitor') {
            this.socket.emit('monitor', [message.action, message.room])
          } else if (message.room === 'case_records') {
            this.socket.emit('monitor', ['case_records'])
          } else if (message.room === 'boxroomdetail') {
            this.socket.emit('join', message.data?.room_id || message.room)
          } else if (message.room === 'boxroom') {
            this.socket.emit('join', 'boxroom')
          } else {
            this.socket.emit('join', message.room)
          }

          console.log(`[SocketStore] 发送房间消息: ${message.room}.${message.action}`)
          return true
        } else {
          // 普通消息
          this.socket.emit('message', message)
          console.log('[SocketStore] 发送普通消息:', message)
          return true
        }
      } catch (error) {
        console.error('[SocketStore] 发送消息失败:', error)
        return false
      }
    },

    setupMessageListener(socket: any) {
      const animationSync = useAnimationSync();

      // 监听通用message事件
      socket.on('message', async (data: any) => {
        this.lastMsgTime = Date.now()
        
        try {
          let parsedData = data
          
          // 如果是字符串，尝试解析为JSON
          if (typeof data === 'string') {
            parsedData = JSON.parse(data)
          }
          
          // 传递给统一处理函数
          await this.handleSocketMessage(parsedData)
        } catch (error) {
          console.error('[🎰SOCKET-STORE] 解析message消息失败:', error, '原始消息:', data)
        }
      })

      // 监听boxroom事件
      socket.on('boxroom', (data: any) => {
        // console.log('[🎰SOCKET-STORE] 收到boxroom事件:', data)
        this.handleSocketMessage(['boxroom', 'update', data])
      })

      // 监听 boxroomdetail 事件（动画同步核心）
      socket.on('boxroomdetail', (data: any) => {
        console.log('[SOCKET-DBG] 📦 boxroomdetail 原始数据:', data)

        try {
          // 后端按文档发送的格式应为 [messageType, action, payload, socketId?]
          if (Array.isArray(data)) {
            // 情况 A：直接就是数组格式 → 按原样处理
            this.handleSocketMessage(data)
          } else if (data && typeof data === 'object') {
            // 情况 B：是对象格式 { action, data, socketId? } → 解析后封装为数组
            const { action = 'update', payload: extractedPayload = data, socketId } = ((): any => {
              // 有些后端实现使用 { action: 'opening_start', data: {...}, socket_id: '...' }
              if ('data' in data || 'payload' in data) {
                return {
                  action: data.action || 'update',
                  payload: (data.data ?? data.payload) || data,
                  socketId: data.socketId || data.socket_id
                }
              }
              // fallback：整个对象当作payload
              return { action: data.action || 'update', payload: data, socketId: undefined }
            })()

            this.handleSocketMessage(['boxroomdetail', action, extractedPayload, socketId])
            console.log('[SOCKET-DBG] 📦 boxroomdetail 解析后触发 handleSocketMessage:', { action, socketId })
          } else {
            // 情况 C：未知格式，降级为 update
            console.log('[SOCKET-DBG] 📦 boxroomdetail 未知格式，降级 update')
            this.handleSocketMessage(['boxroomdetail', 'update', data])
          }
        } catch (err) {
          console.error('[🎰SOCKET-STORE] 处理 boxroomdetail 事件失败:', err)
        }
      })

      // 监听其他对战相关事件
      const battleEvents = ['battle', 'room_update', 'player_joined', 'player_left']
      battleEvents.forEach(eventName => {
        socket.on(eventName, async (data: any) => {
          // console.log(`[🎰SOCKET-STORE] 收到${eventName}事件:`, data)
          try {
            await this.handleSocketMessage([eventName, 'update', data])
          } catch (error) {
            console.error(`[🎰SOCKET-STORE] 处理${eventName}事件失败:`, error)
          }
        })
      })
    },

    /**
     * 处理Socket消息 - 增强版本
     */
    async handleSocketMessage(message: any) {
      try {
        console.log('[🎰SOCKET-STORE] 收到原始消息:', message)

        if (!Array.isArray(message) || message.length < 2) {
          console.log('[🎰SOCKET-STORE] 消息格式无效，跳过处理')
          return
        }
        
        const [messageType, action, payload, timestamp] = message

        console.log(`[🎰SOCKET-STORE] 解析消息: type=${messageType}, action=${action}`, {
          payloadType: typeof payload,
          timestamp,
          hasPayload: !!payload
        })

        // 验证消息时间戳，避免处理过期消息
        if (timestamp && typeof timestamp === 'number') {
          const messageAge = Date.now() - timestamp
          if (messageAge > 30000) { // 30秒过期
            console.warn(`[🎰SOCKET-STORE] 消息过期，跳过处理: ${messageAge}ms`)
            return
          }
        }

        switch (messageType) {
          case 'box':
            await this.handleBoxMessage(action, payload)
            break

          case 'boxroom':
            await this.handleBattleMessage(action, payload)
            break

          case 'boxroomdetail':
            await this.handleBattleDetailMessage(action, payload, timestamp)
            break

          case 'monitor':
            await this.handleMonitorMessage(action, payload)
            break

          case 'case_records':
            await this.handleCaseRecordsMessage(action, payload)
            break

          case 'stats':
            await this.handleStatsMessage(action, payload)
            break

          default:
            console.log(`[🎰SOCKET-STORE] 未知消息类型: ${messageType}`)
            break
        }
      } catch (error) {
        console.error('[🎰SOCKET-STORE] 处理Socket消息失败:', error)
      }
    },

    /**
     * 处理对战房间相关消息
     */
    async handleBattleMessage(action: string | null, payload: any) {
      try {
        // console.log(`[🎰SOCKET-STORE] 处理对战消息 - 动作: ${action}`, payload)
        
        switch (action) {
          case 'new':
            // console.log('[🎰SOCKET-STORE] 🆕 [Battle] 新房间创建')
            if (payload) {
              this.dispatchBattleEvent('new', payload)
            }
            break
            
          case 'update':
            // console.log('[🎰SOCKET-STORE] 🔄 [Battle] 房间更新')
            if (payload) {
              this.dispatchBattleEvent('update', payload)
            }
            break

          case 'start':
            // console.log('[🎰SOCKET-STORE] 🚀 [Battle] 对战开始')
            if (payload) {
              this.dispatchBattleEvent('start', payload)
            }
            break

          case 'cancel':
            // console.log('[🎰SOCKET-STORE] ❌ [Battle] 房间取消')
            if (payload) {
              this.dispatchBattleEvent('cancel', payload)
            }
            break
            
          case 'end':
          case 'remove':
          case 'delete':
            // console.log(`[🎰SOCKET-STORE] 🗑️ [Battle] 房间${action}`)
            if (payload) {
              await this.handleRoomRemove(payload)
            }
            break
            
          case 'player_join':
            // console.log('[🎰SOCKET-STORE] 👤 [Battle] 玩家加入房间')
            if (payload) {
              this.dispatchBattleEvent('update', payload)
            }
            break
            
          case 'player_leave':
            // console.log('[🎰SOCKET-STORE] 🚪 [Battle] 玩家离开房间')
            if (payload) {
              this.dispatchBattleEvent('update', payload)
            }
            break
            
          default:
            // console.log(`[🎰SOCKET-STORE] ❓ [Battle] 未处理的动作: ${action}`)
            // 对于未知动作，默认当作更新处理
            if (payload) {
              this.dispatchBattleEvent('update', payload)
            }
            break
        }
      } catch (error) {
        console.error('[🎰SOCKET-STORE] ❌ [Battle] 处理对战消息时出错:', error)
      }
    },

    /**
     * 处理对战详情相关消息 (boxroomdetail)
     */
    async handleBattleDetailMessage(action: string | null, betData: any, socketId?: string) {
      try {
        console.log(`[SOCKET-DBG] 🎬 handleBattleDetailMessage - action: ${action}`)
        
        switch (action) {
          case 'round':
            // console.log('[🎰SOCKET-STORE] 🎯 [BattleDetail] 回合开启')
            if (betData) {
              // 派发回合开启事件（legacy round -> socket-battle-round）
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('socket-battle-round', {
                  detail: { data: betData, socketId, action }
                }))
              }

              // 去重：同一房间/回合会多次推送 legacy "round"，避免重复派发 opening_start
              const now = Date.now()
              // @ts-ignore - 动态属性存储最后派发时间
              if (this._lastSyntheticOpening && now - this._lastSyntheticOpening < 800) {
                // 800ms 内已派发过，跳过
                break
              }
              // @ts-ignore 保存最后派发时间
              this._lastSyntheticOpening = now

              const syntheticOpeningData = {
                // 生成伪动画 ID：anim_{timestamp}_compat
                animation_id: `anim_${Date.now()}_compat`,
                // 回退字段映射：participants / users / bets
                participants: ((): any[] => {
                  if (Array.isArray(betData)) return betData
                  if (Array.isArray(betData.participants)) return betData.participants
                  if (Array.isArray(betData.users)) return betData.users
                  if (Array.isArray(betData.bets)) return betData.bets
                  return []
                })(),
                // 8秒默认动画时长，供 useBattleAnimations 使用
                animation_duration: 8000,
                server_timestamp: Date.now(),
                // 兼容字段
                ...betData
              }

              console.log('[SOCKET-DBG] 🔧 syntheticOpeningData participants.length=', syntheticOpeningData.participants.length)

              window.dispatchEvent(new CustomEvent('socket:opening_start', {
                detail: { data: syntheticOpeningData, socketId, timestamp: Date.now() }
              }))
            }
            break
            
          case 'end':
            // console.log('[🎰SOCKET-STORE] 🏆 [BattleDetail] 对战结束')
            if (betData) {
              // 在对战动画系统中，"end"动作常作为最终回合结果推送，
              // 为保持与 round_result 统一的解耦事件流，这里同步触发
              // socket:round_result 事件，供动画系统做减速、记录等逻辑处理。
              if (typeof window !== 'undefined') {
                // 1) 触发最终回合结果事件（与 round_result 保持一致）
                window.dispatchEvent(new CustomEvent('socket:round_result', {
                  detail: { data: betData, socketId, action: 'end' }
                }))

                // 2) 继续派发对战结束详情事件，供其他业务使用
                window.dispatchEvent(new CustomEvent('socket-battle-detail-end', {
                  detail: { data: betData, socketId, action }
                }))
              }
            }
            break
            
          case 'round_start':
            // 🎯 回合开始（带时间戳同步）
            if (betData) {
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('socket:round_start', {
                  detail: { data: betData, socketId, action }
                }))
              }
            }
            break

          case 'opening_start':
            const { handleOpeningStart } = useAnimationSync()
            handleOpeningStart(betData)
            break

          case 'animation_progress':
            // 🎯 动画进度同步
            if (betData) {
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('socket:animation_progress', {
                  detail: { data: betData, socketId, action }
                }))
              }
            }
            break

          case 'round_result':
              if (typeof window !== 'undefined') {
              window.dispatchEvent(new CustomEvent('battle:decelerate', { detail: betData }))
            }
            break;

          case 'battle_end':
              if (typeof window !== 'undefined') {
              window.dispatchEvent(new CustomEvent('battle:battle_end', { detail: betData }))
            }
            break;
            
          default:
            // ⚠️ 兼容某些后端直接推送结果而未指定动作
            try {
              if (betData && betData.results && Array.isArray(betData.results)) {
                console.warn('[SOCKET-DBG] 🛟 未知action但检测到results字段，兼容派发round_result')
                if (typeof window !== 'undefined') {
                  window.dispatchEvent(new CustomEvent('socket:round_result', {
                    detail: { data: betData, socketId, action: action || 'unknown' }
                  }))
                }
              }
            } catch (err) {
              console.error('自动兼容 round_result 失败', err)
            }
            break
        }
      } catch (error) {
        console.error('[🎰SOCKET-STORE] ❌ [BattleDetail] 处理对战详情消息时出错:', error)
      }
    },

    /**
     * 处理房间删除/移除
     */
    async handleRoomRemove(payload: any) {
      try {
        // 如果 payload 是字符串，可能是房间 UID
        let roomUid: string
        
        if (typeof payload === 'string') {
          roomUid = payload
        } else if (payload && payload.uid) {
          roomUid = payload.uid
        } else if (payload && payload.room_uid) {
          roomUid = payload.room_uid
        } else {
          console.warn('⚠️ [Battle] 房间删除消息缺少 UID:', payload)
          return
        }
        
        console.log(`🗑️ [Battle] 删除房间: ${roomUid}`)
        
        // 派发房间删除事件，让BattleStore处理
        if (typeof window !== 'undefined') {
          // BattleStore期望：event.detail?.uid || event.detail?.data?.uid || event.detail
          window.dispatchEvent(new CustomEvent('socket-battle-remove', {
            detail: { uid: roomUid, roomUid, data: { uid: roomUid } }
          }))
        }
        
        console.log(`✅ [Battle] 房间删除事件已派发: ${roomUid}`)
        
      } catch (error) {
        console.error('❌ [Battle] 处理房间删除时出错:', error)
      }
    },

    /**
     * 派发对战事件
     */
    dispatchBattleEvent(action: string, payload: any) {
      try {
        // console.log(`[🎰SOCKET-STORE] 🚀 开始派发对战事件: socket-battle-${action}`)
        // console.log(`[🎰SOCKET-STORE] 📦 事件数据:`, payload)
        
        // 派发通用对战事件，确保数据格式与BattleStore期望的一致
        if (typeof window !== 'undefined') {
          // BattleStore期望的格式：event.detail?.data || event.detail
          // 为了兼容性，我们同时提供两种格式
          const eventName = `socket-battle-${action}`
          const customEvent = new CustomEvent(eventName, {
            detail: payload  // 直接传递payload，因为BattleStore会使用 event.detail?.data || event.detail
          })
          
          // console.log(`[🎰SOCKET-STORE] 📡 派发事件: ${eventName}`)
          window.dispatchEvent(customEvent)
          // console.log(`[🎰SOCKET-STORE] ✅ 事件派发完成: ${eventName}`)
        } else {
          console.warn(`[🎰SOCKET-STORE] ⚠️ window对象不可用，无法派发事件`)
        }
        
        // console.log(`[🎰SOCKET-STORE] 📊 事件摘要: socket-battle-${action}`, {
        //   uid: payload?.uid,
        //   shortId: payload?.short_id,
        //   state: payload?.state,
        //   joinerCount: payload?.joiner_count
        // })
        
      } catch (error) {
        console.error('[🎰SOCKET-STORE] ❌ 派发对战事件失败:', error)
      }
    },

    /**
     * 处理房间状态更新
     */
    async handleRoomStatusUpdate(payload: any) {
      if (payload) {
        this.dispatchBattleEvent('update', payload)
      }
    },

    /**
     * 处理玩家加入
     */
    async handlePlayerJoined(payload: any) {
      if (payload) {
        console.log('👥 [Socket] 玩家加入，更新房间数据')
        this.dispatchBattleEvent('update', payload)
      }
    },

    /**
     * 处理玩家离开
     */
    async handlePlayerLeft(payload: any) {
      if (payload) {
        console.log('👋 [Socket] 玩家离开，更新房间数据')
        this.dispatchBattleEvent('update', payload)
      }
    },

    /**
     * 处理对战开始
     */
    async handleBattleStarted(payload: any) {
      if (payload) {
        console.log('⚔️ [Socket] 对战开始，更新房间状态')
        this.dispatchBattleEvent('update', payload)
      }
    },

    /**
     * 处理对战结束
     */
    async handleBattleFinished(payload: any) {
      if (payload) {
        console.log('🏁 [Socket] 对战结束，更新房间状态')
        this.dispatchBattleEvent('update', payload)
        
        // 延迟删除已结束的房间（给用户时间查看结果）
        if (payload.uid || payload.room_uid) {
          const roomUid = payload.uid || payload.room_uid
          // 使用安全的 setTimeout 调用
          const timer = (globalThis as any).setTimeout || ((globalThis as any).window?.setTimeout)
          if (typeof timer === 'function') {
            timer(() => {
              console.log(`⏰ [Battle] 延迟删除已结束的房间: ${roomUid}`)
              this.handleRoomRemove(roomUid)
            }, 10000) // 10秒后移除
          } else {
            console.warn('⚠️ [Battle] setTimeout 不可用，跳过延迟删除')
          }
        }
      }
    },

    getConnectionStatus() {
      if (this.isConnected) {
        return '已连接'
      } else if (this.reconnectAttempts > 0) {
        return `重连中 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`
      } else if (this.connectionError) {
        return this.connectionError
      } else {
        return '连接中...'
      }
    },

    // 请求开箱记录数据
    requestCaseRecords() {
      if (!this.socket || !this.isConnected) {
        console.warn('[SocketStore] 无法请求开箱记录：Socket未连接')
        return
      }

      try {
        // console.log('[SocketStore] 请求开箱记录数据...') // 调试时可启用
        
        // 发送多种可能的事件名，提高兼容性
        const eventNames = [
          'request-case-records',
          'get-case-records', 
          'request-data',
          'get-data',
          'case-records',
          'records',
          'openings'
        ]
        
        eventNames.forEach(eventName => {
          try {
            this.socket.emit(eventName)
            // console.log(`[SocketStore] 已发送事件: ${eventName}`) // 调试时可启用
          } catch (error) {
            console.error(`[SocketStore] 发送事件 ${eventName} 失败:`, error)
          }
        })
        
      } catch (error) {
        console.error('[SocketStore] 请求开箱记录失败:', error)
      }
    },

    // 请求统计数据
    requestStatsData() {
      if (!this.socket || !this.isConnected) {
        console.warn('[SocketStore] 无法请求统计数据：Socket未连接')
        return
      }

      try {
        // console.log('[SocketStore] 请求统计数据...') // 调试时可启用
        
        // 发送统计数据请求
        this.socket.emit('monitor', 'get_stats')
        this.socket.emit('get-stats')
        this.socket.emit('stats')
        
        // console.log('[SocketStore] 统计数据请求已发送') // 调试时可启用
      } catch (error) {
        console.error('[SocketStore] 请求统计数据失败:', error)
      }
    },

    /**
     * 处理开箱相关消息
     */
    async handleBoxMessage(action: string | null, payload: any) {
      try {
        // console.log(`[🎰SOCKET-STORE] 处理开箱消息 - 动作: ${action}`, payload)
        
        if (payload) {
          // 处理开箱消息，更新case records
          if (Array.isArray(payload)) {
            this.updateCaseRecords(payload)
          } else {
            // 单个开箱记录
            if (Array.isArray(this.caseRecords)) {
              this.caseRecords.unshift(payload)
              // 限制记录数量，避免内存泄漏
              if (this.caseRecords.length > 100) {
                this.caseRecords = this.caseRecords.slice(0, 100)
              }
            }
          }
          
          // 派发事件给CaseStore
          if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('socket-box', {
              detail: { data: payload }
            }))
          }
        }
      } catch (error) {
        console.error('[🎰SOCKET-STORE] 处理开箱消息失败:', error)
      }
    },

    /**
     * 处理监控数据消息
     */
    async handleMonitorMessage(action: string | null, payload: any) {
      try {
        console.log(`[🎰SOCKET-STORE] 处理监控消息 - 动作: ${action}`, {
          payloadType: typeof payload,
          isArray: Array.isArray(payload),
          hasPayload: !!payload
        })

        switch (action) {
          case 'update':
          case 'stats':
          case 'get_stats':
            if (payload && typeof payload === 'object') {
              // 验证和增强数据完整性
              const validData = {
                user_number: payload.user_number || this.statsData?.user_number || 0,
                case_number: payload.case_number || this.statsData?.case_number || 0,
                total_value: payload.total_value || this.statsData?.total_value || 0,
                ...payload // 保留其他字段
              }

              console.log('[🎰SOCKET-STORE] 处理后的监控数据:', validData)
              this.setStatsData(validData)

              // 派发全局事件给组件
              if (typeof window !== 'undefined') {
                const eventName = action === 'update' ? 'socket:monitor:update' : 'socket:monitor:stats'
                window.dispatchEvent(new CustomEvent(eventName, {
                  detail: { data: validData, action }
                }))
              }
            }
            break

          case 'case_records':
          case 'records':
            if (Array.isArray(payload)) {
              console.log(`[🎰SOCKET-STORE] 更新开箱记录列表: ${payload.length}条`)
              this.setCaseRecords(payload)

              // 派发开箱记录更新事件
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('socket:case_records:update', {
                  detail: { data: payload, action: 'list' }
                }))
              }
            } else if (payload && typeof payload === 'object') {
              console.log(`[🎰SOCKET-STORE] 添加单条开箱记录: ${payload.id || '未知ID'}`)
              this.addCaseRecord(payload)

              // 派发新开箱记录事件
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('socket:case_records:new', {
                  detail: { data: payload, action: 'new' }
                }))
              }
            }
            break

          case 'join_success':
            console.log(`[🎰SOCKET-STORE] 成功加入监控房间: ${payload}`)
            break

          default:
            // 兼容旧的处理方式
            console.log(`[🎰SOCKET-STORE] 未知监控动作: ${action}`)
            if (payload) {
              this.updateMonitorData(payload)

              // 派发事件给CaseStore
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('socket-monitor', {
                  detail: payload
                }))
              }
            }
            break
        }
      } catch (error) {
        console.error('[🎰SOCKET-STORE] 处理监控消息失败:', error)
      }
    },

    /**
     * 处理开箱记录消息
     */
    async handleCaseRecordsMessage(action: string | null, payload: any) {
      try {
        // console.log(`[🎰SOCKET-STORE] 处理开箱记录消息 - 动作: ${action}`, payload)

        switch (action) {
          case 'update':
          case 'new':
            if (payload) {
              // 无论是数组还是单条记录，统一追加更新
              if (Array.isArray(payload)) {
                this.updateCaseRecords(payload)
              } else {
                this.updateCaseRecords([payload])
              }

              // 派发事件给开箱详情页
              if (typeof window !== 'undefined') {
                window.dispatchEvent(new CustomEvent('socket:case_records:update', {
                  detail: {
                    action,
                    data: payload
                  }
                }))
              }
            }
            break

          case 'join_success':
            console.log(`[🎰SOCKET-STORE] 成功加入开箱记录房间: ${payload}`)
            break

          default:
            // 兼容旧的处理方式
            if (payload) {
              if (Array.isArray(payload)) {
                this.updateCaseRecords(payload)
              } else {
                this.updateCaseRecords([payload])
              }
            }
            break
        }
      } catch (error) {
        console.error('[🎰SOCKET-STORE] 处理开箱记录消息失败:', error)
      }
    },

    /**
     * 处理统计数据消息
     */
    async handleStatsMessage(action: string | null, payload: any) {
      try {
        if (payload) {
          this.setStatsData(payload)
        }
      } catch (error) {
        console.error('[🎰SOCKET-STORE] 处理统计消息失败:', error)
      }
    }
  }
})