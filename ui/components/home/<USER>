<template>
  <div class="w-full">
    <!-- PC端统计卡片 -->
    <div v-if="!isMobile" class="relative p-8 bg-gray-800/40 backdrop-blur-xl border border-gray-700/30 rounded-xl overflow-hidden">
      <!-- 装饰背景元素 -->
      <div class="absolute inset-0 pointer-events-none z-0">
        <div class="bg-decoration bg-decoration-1"></div>
        <div class="bg-decoration bg-decoration-2"></div>
        <div class="bg-decoration bg-decoration-3"></div>
      </div>

      <!-- 使用现代化网格布局 -->
      <div v-if="!isLoading && hasValidData" class="relative z-10">
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
          <!-- 用户数据卡片 -->
          <div class="group">
            <div 
              class="stats-card relative h-48 p-6 bg-white/5 border border-white/10 rounded-xl backdrop-blur-xl transition-all duration-500 hover:scale-102 hover:border-blue-400/50 hover:bg-blue-400/5 hover:shadow-lg hover:shadow-blue-400/20 overflow-hidden"
              :class="{ 
                'animate-highlight': usersChanged, 
                'positive-change': usersChangeType === 'positive', 
                'negative-change': usersChangeType === 'negative' 
              }"
            >
              <div class="card-glow"></div>
              <div class="relative z-10 h-full flex flex-col">
                <!-- 左上角标题和图标 -->
                <div class="flex items-center gap-3 mb-4">
                  <div class="flex items-center justify-center w-12 h-12 rounded-xl bg-blue-500/20 border border-blue-400/30 group-hover:scale-110 transition-transform duration-300">
                    <Icon name="heroicons:users" class="w-6 h-6 text-blue-400" />
                  </div>
                  <h3 class="text-sm font-semibold text-white/70 uppercase tracking-wider">{{ $t("stats.total_users") }}</h3>
                </div>
                
                <!-- 突出显示的数字 -->
                <div class="flex-1 flex items-center justify-center">
                  <div class="text-3xl font-extrabold text-white drop-shadow-lg" :data-value="formattedUsers">
                    {{ formattedUsers }}
                  </div>
                  <div class="flex items-center" v-if="usersChanged">
                    <Icon 
                      :name="usersChangeType === 'positive' ? 'heroicons:arrow-trending-up' : 'heroicons:arrow-trending-down'" 
                      class="w-5 h-5 text-green-400"
                      :class="usersChangeType === 'positive' ? 'text-green-400' : 'text-red-400'"
                    />
                  </div>
                </div>
              </div>
              <div class="card-border"></div>
            </div>
          </div>

          <!-- 开箱数据卡片 -->
          <div class="group">
            <div 
              class="stats-card relative h-48 p-6 bg-white/5 border border-white/10 rounded-xl backdrop-blur-xl transition-all duration-500 hover:scale-102 hover:border-orange-400/50 hover:bg-orange-400/5 hover:shadow-lg hover:shadow-orange-400/20 overflow-hidden"
              :class="{ 
                'animate-highlight': casesChanged, 
                'positive-change': caseChangeType === 'positive', 
                'negative-change': caseChangeType === 'negative' 
              }"
            >
              <div class="card-glow"></div>
              <div class="relative z-10 h-full flex flex-col">
                <div class="flex items-center gap-3 mb-4">
                  <div class="flex items-center justify-center w-12 h-12 rounded-xl bg-orange-500/20 border border-orange-400/30 group-hover:scale-110 transition-transform duration-300">
                    <Icon name="game-icons:locked-chest" class="w-6 h-6 text-orange-400" />
                  </div>
                  <h3 class="text-sm font-semibold text-white/70 uppercase tracking-wider">{{ $t("stats.cases_opened") }}</h3>
                </div>
                
                <div class="flex-1 flex items-center justify-center">
                  <div class="text-3xl font-extrabold text-white drop-shadow-lg" :data-value="formattedCases">
                    {{ formattedCases }}
                  </div>
                  <div class="flex items-center" v-if="casesChanged">
                    <Icon 
                      :name="caseChangeType === 'positive' ? 'heroicons:arrow-trending-up' : 'heroicons:arrow-trending-down'" 
                      class="w-5 h-5"
                      :class="caseChangeType === 'positive' ? 'text-green-400' : 'text-red-400'"
                    />
                  </div>
                </div>
              </div>
              <div class="card-border"></div>
            </div>
          </div>

          <!-- 对战数据卡片 -->
          <div class="group">
            <div 
              class="stats-card relative h-48 p-6 bg-white/5 border border-white/10 rounded-xl backdrop-blur-xl transition-all duration-500 hover:scale-102 hover:border-purple-400/50 hover:bg-purple-400/5 hover:shadow-lg hover:shadow-purple-400/20 overflow-hidden"
              :class="{ 
                'animate-highlight': battleChanged, 
                'positive-change': battleChangeType === 'positive', 
                'negative-change': battleChangeType === 'negative' 
              }"
            >
              <div class="card-glow"></div>
              <div class="relative z-10 h-full flex flex-col">
                <div class="flex items-center gap-3 mb-4">
                  <div class="flex items-center justify-center w-12 h-12 rounded-xl bg-purple-500/20 border border-purple-400/30 group-hover:scale-110 transition-transform duration-300">
                    <Icon name="game-icons:crossed-swords" class="w-6 h-6 text-purple-400" />
                  </div>
                  <h3 class="text-sm font-semibold text-white/70 uppercase tracking-wider">{{ $t("stats.battles_played") }}</h3>
                </div>
                
                <div class="flex-1 flex items-center justify-center">
                  <div class="text-3xl font-extrabold text-white drop-shadow-lg" :data-value="formattedBattles">
                    {{ formattedBattles }}
                  </div>
                  <div class="flex items-center" v-if="battleChanged">
                    <Icon 
                      :name="battleChangeType === 'positive' ? 'heroicons:arrow-trending-up' : 'heroicons:arrow-trending-down'" 
                      class="w-5 h-5"
                      :class="battleChangeType === 'positive' ? 'text-green-400' : 'text-red-400'"
                    />
                  </div>
                </div>
              </div>
              <div class="card-border"></div>
            </div>
          </div>

          <!-- 在线用户卡片 -->
          <div class="group">
            <div 
              class="stats-card relative h-48 p-6 bg-white/5 border border-white/10 rounded-xl backdrop-blur-xl transition-all duration-500 hover:scale-102 hover:border-green-400/50 hover:bg-green-400/5 hover:shadow-lg hover:shadow-green-400/20 overflow-hidden"
              :class="{ 
                'animate-highlight': onlineChanged, 
                'positive-change': onlineChangeType === 'positive', 
                'negative-change': onlineChangeType === 'negative' 
              }"
            >
              <div class="card-glow"></div>
              <div class="relative z-10 h-full flex flex-col">
                <div class="flex items-center gap-3 mb-4">
                  <div class="flex items-center justify-center w-12 h-12 rounded-xl bg-green-500/20 border border-green-400/30 group-hover:scale-110 transition-transform duration-300">
                    <Icon name="heroicons:signal" class="w-6 h-6 text-green-400" />
                  </div>
                  <h3 class="text-sm font-semibold text-white/70 uppercase tracking-wider">{{ $t("stats.online_users") }}</h3>
                </div>
                
                <div class="flex-1 flex items-center justify-center">
                  <div class="text-3xl font-extrabold text-white drop-shadow-lg flex items-center gap-2" :data-value="formattedOnline">
                    {{ formattedOnline }}
                   
                  </div>
                  <div class="flex items-center" v-if="onlineChanged">
                    <Icon 
                      :name="onlineChangeType === 'positive' ? 'heroicons:arrow-trending-up' : 'heroicons:arrow-trending-down'" 
                      class="w-5 h-5"
                      :class="onlineChangeType === 'positive' ? 'text-green-400' : 'text-red-400'"
                    />
                  </div>
                </div>
              </div>
              <div class="card-border"></div>
            </div>
          </div>
        </div>
      </div>

      <!-- PC端骨架屏 -->
      <div v-else class="relative z-10">
        <div class="grid grid-cols-2 lg:grid-cols-4 gap-8">
          <div class="group" v-for="i in 4" :key="i">
            <div class="relative h-48 p-6 bg-white/5 border border-white/10 rounded-xl backdrop-blur-xl animate-pulse">
              <div class="flex items-center gap-3 mb-4">
                <div class="w-12 h-12 rounded-xl bg-gray-600/50"></div>
                <div class="h-4 bg-gray-600/50 rounded-lg w-24"></div>
              </div>
              <div class="flex-1 flex items-center justify-center">
                <div class="h-10 bg-gray-600/50 rounded-lg w-20"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 移动端统计卡片 -->
    <div v-else class="p-4 space-y-4">
      <div v-if="!isLoading && hasValidData" class="space-y-4">
        <!-- 移动端统计卡片 -->
        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center gap-3 p-4 bg-white/5 border border-white/10 rounded-xl backdrop-blur-xl">
            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-blue-500/20 border border-blue-400/30">
              <Icon name="heroicons:users" class="w-5 h-5 text-blue-400" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-lg font-bold text-white truncate">{{ formattedUsers }}</div>
              <div class="text-xs text-white/60 truncate">{{ $t("stats.total_users") }}</div>
            </div>
          </div>
          
          <div class="flex items-center gap-3 p-4 bg-white/5 border border-white/10 rounded-xl backdrop-blur-xl">
            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-orange-500/20 border border-orange-400/30">
              <Icon name="game-icons:locked-chest" class="w-5 h-5 text-orange-400" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-lg font-bold text-white truncate">{{ formattedCases }}</div>
              <div class="text-xs text-white/60 truncate">{{ $t("stats.cases_opened") }}</div>
            </div>
          </div>
        </div>

        <div class="grid grid-cols-2 gap-4">
          <div class="flex items-center gap-3 p-4 bg-white/5 border border-white/10 rounded-xl backdrop-blur-xl">
            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-purple-500/20 border border-purple-400/30">
              <Icon name="game-icons:crossed-swords" class="w-5 h-5 text-purple-400" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-lg font-bold text-white truncate">{{ formattedBattles }}</div>
              <div class="text-xs text-white/60 truncate">{{ $t("stats.battles_played") }}</div>
            </div>
          </div>
          
          <div class="flex items-center gap-3 p-4 bg-white/5 border border-white/10 rounded-xl backdrop-blur-xl">
            <div class="flex items-center justify-center w-10 h-10 rounded-lg bg-green-500/20 border border-green-400/30">
              <Icon name="heroicons:signal" class="w-5 h-5 text-green-400" />
            </div>
            <div class="flex-1 min-w-0">
              <div class="text-lg font-bold text-white flex items-center gap-1 truncate">
                {{ formattedOnline }}
                <span class="mobile-online-dot"></span>
              </div>
              <div class="text-xs text-white/60 truncate">{{ $t("stats.online_users") }}</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 移动端骨架屏 -->
      <div v-else class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div v-for="i in 2" :key="i" class="flex items-center gap-3 p-4 bg-white/5 border border-white/10 rounded-xl backdrop-blur-xl animate-pulse">
            <div class="w-10 h-10 rounded-lg bg-gray-600/50"></div>
            <div class="flex-1 space-y-2">
              <div class="h-5 bg-gray-600/50 rounded w-16"></div>
              <div class="h-3 bg-gray-600/50 rounded w-20"></div>
            </div>
          </div>
        </div>
        <div class="grid grid-cols-2 gap-4">
          <div v-for="i in 2" :key="i + 2" class="flex items-center gap-3 p-4 bg-white/5 border border-white/10 rounded-xl backdrop-blur-xl animate-pulse">
            <div class="w-10 h-10 rounded-lg bg-gray-600/50"></div>
            <div class="flex-1 space-y-2">
              <div class="h-5 bg-gray-600/50 rounded w-16"></div>
              <div class="h-3 bg-gray-600/50 rounded w-20"></div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useSocketStore } from '~/stores/socket'
import { monitorApi } from '~/services/monitor-api'
import { useBreakpoints } from '@vueuse/core'
import { useSocketRoomManager, socketRooms, socketEvents } from '~/utils/socket-manager'

// 响应式断点检测
const breakpoints = useBreakpoints({
  mobile: 768,
  tablet: 1024,
  desktop: 1280
})

const isMobile = breakpoints.smaller('tablet')

// 获取Socket Store
const socketStore = useSocketStore()
const { t } = useI18n()
const socketRoomManager = useSocketRoomManager()

// 响应式状态
const isLoading = ref(true)
const casesChanged = ref(false)
const battleChanged = ref(false)
const usersChanged = ref(false)
const onlineChanged = ref(false)

const caseChangeType = ref<'positive' | 'negative'>('positive')
const battleChangeType = ref<'positive' | 'negative'>('positive')
const usersChangeType = ref<'positive' | 'negative'>('positive')
const onlineChangeType = ref<'positive' | 'negative'>('positive')

// 记录时间戳
let lastRefreshTime = 0
let lastDataUpdateTime = 0
const MIN_REFRESH_INTERVAL = 15000 // 最小刷新间隔，15秒

// 检查是否有有效数据
const hasValidData = computed(() => {
  return socketStore.statsData && 
         socketStore.statsData.case_number > 0 &&
         socketStore.statsData.user_number > 0
})

// 计算属性 - 直接使用原始数据
const formattedUsers = computed(() => 
  (socketStore.statsData?.user_number || 0).toLocaleString()
)

const formattedCases = computed(() => 
  (socketStore.statsData?.case_number || 0).toLocaleString()
)

const formattedBattles = computed(() => 
  (socketStore.statsData?.battle_number || 0).toLocaleString()
)

const formattedOnline = computed(() => 
  (socketStore.statsData?.online_number || 0).toLocaleString()
)

// 静默刷新数据
const silentRefreshData = async () => {
  try {
    if (socketStore.socket && socketStore.isConnected) {
      // 使用socket请求最新统计数据 - 使用正确的格式
      socketStore.emit('monitor', 'get_stats')
      socketStore.requestStatsData()
      return
    }
    
    // 通过API获取数据的逻辑
    await fetchDataFromApi()
  } catch (error) {
    console.error('[HomeStats] 静默刷新数据失败:', error)
  }
}

// 从API获取数据
const fetchDataFromApi = async () => {
  // 如果已经有数据，不重复获取
  if (!isLoading.value && socketStore.statsData && 
      socketStore.statsData.user_number > 0 && 
      socketStore.statsData.case_number > 0) {
    return
  }

  try {
    isLoading.value = true
    
    // 使用monitorApi获取统计数据
    const result = await monitorApi.getStatsData()
    
    if (result.success && result.data && result.data.stats) {
      // 更新统计数据
      socketStore.setStatsData(result.data.stats)
    } else {
      console.error('[HomeStats] 获取统计数据失败:', result.message)
    }
  } catch (err) {
    console.error('[HomeStats] API请求失败:', err)
  } finally {
    // 确保在获取数据后结束加载状态
    isLoading.value = false
  }
}

// 处理页面可见性变化
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    const now = Date.now()
    if (now - lastDataUpdateTime > MIN_REFRESH_INTERVAL * 2) {
      silentRefreshData()
      lastRefreshTime = now
    }
  }
}

// 监听统计数据变化
watch(() => socketStore.statsData, (newStats, oldStats) => {
  lastDataUpdateTime = Date.now()
  isLoading.value = false
  
  if (!oldStats || !newStats) return
  
  // 用户数变化
  if (oldStats.user_number !== newStats.user_number) {
    usersChanged.value = true
    usersChangeType.value = newStats.user_number > oldStats.user_number ? 'positive' : 'negative'
    setTimeout(() => {
      usersChanged.value = false
    }, 2000)
  }
  
  // 开箱数变化
  if (oldStats.case_number !== newStats.case_number) {
    casesChanged.value = true
    caseChangeType.value = newStats.case_number > oldStats.case_number ? 'positive' : 'negative'
    setTimeout(() => {
      casesChanged.value = false
    }, 2000)
  }
  
  // 对战数变化
  if (oldStats.battle_number !== newStats.battle_number) {
    battleChanged.value = true
    battleChangeType.value = newStats.battle_number > oldStats.battle_number ? 'positive' : 'negative'
    setTimeout(() => {
      battleChanged.value = false
    }, 2000)
  }
  
  // 在线用户变化
  if (oldStats.online_number !== newStats.online_number) {
    onlineChanged.value = true
    onlineChangeType.value = newStats.online_number > oldStats.online_number ? 'positive' : 'negative'
    setTimeout(() => {
      onlineChanged.value = false
    }, 2000)
  }
}, { deep: true })

// 处理Socket消息事件 - 增强版本
const handleSocketMessage = (event: Event) => {
  try {
    const customEvent = event as CustomEvent
    const data = customEvent.detail

    console.log('[HomeStats] 收到Socket消息:', data)

    // 处理统计数据更新 - 支持多种数据格式
    if (data && data.stats) {
      console.log('[HomeStats] 处理stats字段数据:', data.stats)
      socketStore.setStatsData(data.stats)
    } else if (data && (data.user_number !== undefined || data.case_number !== undefined)) {
      console.log('[HomeStats] 处理直接统计数据:', {
        user_number: data.user_number,
        case_number: data.case_number,
        total_value: data.total_value
      })
      socketStore.setStatsData(data)
    } else if (data && data.event === 'monitor') {
      console.log('[HomeStats] 处理monitor事件数据:', data.data)
      socketStore.setStatsData(data.data)
    }

    // 更新数据时间
    lastDataUpdateTime = Date.now()
    isLoading.value = false

    console.log('[HomeStats] 统计数据更新完成，当前数据:', socketStore.statsData)
  } catch (error) {
    console.error('[HomeStats] 处理Socket消息失败:', error)
  }
}

// 新的事件处理函数 - 增强版本
const handleMonitorUpdate = (event: Event) => {
  try {
    const customEvent = event as CustomEvent
    const data = customEvent.detail?.data

    if (data && typeof data === 'object') {
      console.log('[HomeStats] 收到监控更新:', data)

      // 验证数据完整性
      const validData = {
        user_number: data.user_number || socketStore.statsData?.user_number || 0,
        case_number: data.case_number || socketStore.statsData?.case_number || 0,
        total_value: data.total_value || socketStore.statsData?.total_value || 0,
        ...data // 保留其他字段
      }

      console.log('[HomeStats] 处理后的监控数据:', validData)
      socketStore.setStatsData(validData)
      lastDataUpdateTime = Date.now()
      isLoading.value = false
    }
  } catch (error) {
    console.error('[HomeStats] 处理监控更新失败:', error)
  }
}

const handleMonitorStats = (event: Event) => {
  try {
    const customEvent = event as CustomEvent
    const data = customEvent.detail?.data

    if (data && typeof data === 'object') {
      console.log('[HomeStats] 收到统计数据:', data)

      // 验证数据完整性
      const validData = {
        user_number: data.user_number || socketStore.statsData?.user_number || 0,
        case_number: data.case_number || socketStore.statsData?.case_number || 0,
        total_value: data.total_value || socketStore.statsData?.total_value || 0,
        ...data // 保留其他字段
      }

      console.log('[HomeStats] 处理后的统计数据:', validData)
      socketStore.setStatsData(validData)
      lastDataUpdateTime = Date.now()
      isLoading.value = false
    }
  } catch (error) {
    console.error('[HomeStats] 处理统计数据失败:', error)
  }
}

// 处理Socket连接事件
const handleSocketConnected = (event?: any) => {
  // 加入监控房间
  socketRoomManager.joinRoom(socketRooms.monitor)

  // 判断距离上次刷新时间
  const now = Date.now()
  if (now - lastRefreshTime > MIN_REFRESH_INTERVAL) {
    // 如果超过最小间隔，才进行静默更新
    silentRefreshData()
    lastRefreshTime = now
  }
}

// 处理Socket断开事件
const handleSocketDisconnected = () => {
}

// 设置事件监听器
const setupEventListeners = () => {
  if (typeof window === 'undefined') return

  // 使用新的Socket事件系统
  socketRoomManager.addEventListener(socketEvents.monitor.update, handleMonitorUpdate)
  socketRoomManager.addEventListener(socketEvents.monitor.stats, handleMonitorStats)

  // 监听Socket消息事件 - 保留兼容性
  window.addEventListener('socket:message', handleSocketMessage)
  window.addEventListener('socket:connected', handleSocketConnected)
  window.addEventListener('socket:disconnected', handleSocketDisconnected)

  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)

  // 监听socket状态变化事件（保留兼容性）
  window.addEventListener('socket-ready', handleSocketReady)

  // 监听首页发出的统计数据更新事件
  window.addEventListener('stats-data-updated', handleStatsDataUpdated)
}

// 移除事件监听器
const removeEventListeners = () => {
  if (typeof window === 'undefined') return
  
  // 移除Socket事件监听
  window.removeEventListener('socket:message', handleSocketMessage)
  window.removeEventListener('socket:connected', handleSocketConnected)
  window.removeEventListener('socket:disconnected', handleSocketDisconnected)
  
  // 移除页面可见性变化监听
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  
  // 移除socket事件监听（保留兼容性）
  window.removeEventListener('socket-ready', handleSocketReady)
  
  // 移除首页发出的统计数据更新事件监听
  window.removeEventListener('stats-data-updated', handleStatsDataUpdated)
}

// 处理socket就绪事件
const handleSocketReady = (event: any) => {
  // 判断距离上次刷新时间
  const now = Date.now()
  if (now - lastRefreshTime > MIN_REFRESH_INTERVAL) {
    // 如果超过最小间隔，才进行静默更新
    silentRefreshData()
    lastRefreshTime = now
  }
}



// 处理首页发出的统计数据更新事件
const handleStatsDataUpdated = (event: any) => {
  try {
    // 记录数据更新时间
    lastDataUpdateTime = Date.now()
    
    // 如果当前组件正在显示，则不做任何处理，因为数据已经在 socketStore 中更新
    // 如果之前的数据加载状态是 true，则设置为 false
    if (isLoading.value) {
      isLoading.value = false
    }
  } catch (error) {
    console.error('[HomeStats] 处理统计数据更新事件失败:', error)
  }
}

// 监听Socket连接状态变化
watch(
  () => socketStore.isConnected,
  (isConnected) => {
    if (isConnected && socketStore.socket) {
      // 检查是否需要刷新数据
      const now = Date.now()
      if (now - lastDataUpdateTime > MIN_REFRESH_INTERVAL * 2) {
        // console.log('[HomeStats] Socket连接已建立，请求统计数据') // 调试时可启用
        socketStore.emit('monitor', 'get_stats')
      }
    } else {
      // console.log('[HomeStats] Socket已断开连接') // 调试时可启用
    }
  }
)

// 生命周期
onMounted(() => {
  // console.log('[HomeStats] 组件挂载') // 调试时可启用
  // 初始化事件监听
  setupEventListeners()
  
  // 首次加载数据
  if (socketStore.statsData && 
      socketStore.statsData.user_number > 0 && 
      socketStore.statsData.case_number > 0) {
    // 已有数据，直接使用
    isLoading.value = false
    lastDataUpdateTime = Date.now()
  } else {
    // 没有数据，尝试加载
    silentRefreshData()
  }
  
  // 记录初始刷新时间
  lastRefreshTime = Date.now()
})

onUnmounted(() => {
  // console.log('[HomeStats] 组件卸载') // 调试时可启用
  // 清理事件监听器
  removeEventListeners()
})

// 处理组件被keep-alive缓存的情况
onActivated(() => {
  // console.log('[HomeStats] 组件被激活') // 调试时可启用
  
  // 设置事件监听器
  setupEventListeners()
  
  // 检查距离上次数据更新时间
  const now = Date.now()
  if (now - lastDataUpdateTime > MIN_REFRESH_INTERVAL * 3) { // 增加时间间隔
    // console.log('[HomeStats] 组件激活：数据可能已过时，静默刷新') // 调试时可启用
    silentRefreshData()
    lastRefreshTime = now
  } else {
    // console.log('[HomeStats] 组件激活：数据较新，无需刷新') // 调试时可启用
  }
})

onDeactivated(() => {
  // console.log('[HomeStats] 组件被停用') // 调试时可启用
  // 清理资源
  removeEventListeners()
})
</script>

<style lang="scss" scoped>
// 装饰背景动画
.bg-decoration {
  position: absolute;
  border-radius: 50%;
  filter: blur(60px);
  opacity: 0.3;
  animation: float 6s ease-in-out infinite;
  
  &.bg-decoration-1 {
    top: -20%;
    right: -10%;
    width: 200px;
    height: 200px;
    background: radial-gradient(circle, #00A8FF, transparent);
    animation-delay: 0s;
  }
  
  &.bg-decoration-2 {
    bottom: -20%;
    left: -10%;
    width: 150px;
    height: 150px;
    background: radial-gradient(circle, #FF4D00, transparent);
    animation-delay: 2s;
  }
  
  &.bg-decoration-3 {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, #7149C6, transparent);
    animation-delay: 4s;
  }
}

// 卡片发光效果
.card-glow {
  position: absolute;
  top: -50%;
  left: -50%;
  right: -50%;
  bottom: -50%;
  background: radial-gradient(circle, rgba(0, 168, 255, 0.15), transparent 70%);
  opacity: 0;
  transition: all 0.5s ease;
  pointer-events: none;
  z-index: 0;
}

.stats-card:hover .card-glow {
  opacity: 1;
  transform: scale(1.1);
}

// 卡片边框效果
.card-border {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: inherit;
  padding: 1px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), transparent, rgba(255, 255, 255, 0.1));
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.stats-card:hover .card-border {
  opacity: 1;
}

// 在线指示器
.online-indicator {
  display: inline-block;
  width: 0.5rem;
  height: 0.5rem;
  background: #22C55E;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
  box-shadow: 0 0 10px rgba(34, 197, 94, 0.5);
}

// 移动端在线指示器
.mobile-online-dot {
  display: inline-block;
  width: 0.375rem;
  height: 0.375rem;
  background: #22C55E;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
  box-shadow: 0 0 8px rgba(34, 197, 94, 0.5);
}

// 变化动画
.positive-change {
  .text-3xl, .text-lg {
    color: #22C55E !important;
  }
}

.negative-change {
  .text-3xl, .text-lg {
    color: #EF4444 !important;
  }
}

// 高亮动画
.animate-highlight {
  animation: highlight 2s ease-in-out;
}

// 动画关键帧
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(1.1);
  }
}

@keyframes highlight {
  0%, 100% {
    transform: scale(1);
    box-shadow: 0 0 0 rgba(0, 168, 255, 0);
  }
  50% {
    transform: scale(1.02);
    box-shadow: 0 0 20px rgba(0, 168, 255, 0.3);
  }
}

// 自定义hover缩放
.hover\:scale-102:hover {
  transform: scale(1.02);
}
</style> 


