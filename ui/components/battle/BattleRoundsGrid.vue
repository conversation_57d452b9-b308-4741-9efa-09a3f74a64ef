<template>
  <BattleItemsGrid
    :items="roundItems"
    :max-players="maxPlayers"
    :convert-to-skin-item="convertToSkinItem"
    type="rounds"
    :total-rounds="totalRounds"
    :current-round="battleStateSync.currentRound"
    :completed-rounds="battleStateSync.completedRounds"
    :is-battle-started="battleStateSync.isBattleStarted"
    :is-battle-finished="battleStateSync.isBattleFinished"
  />
</template>

<script setup lang="ts">
import { computed, watch, inject } from 'vue'
import type { BattleBet, BattleItem } from "~/types/battle";

// 🎯 Props定义
interface Props {
  player: BattleBet;
  totalRounds: number;
  maxPlayers: number;
  convertToSkinItem: (item: BattleItem) => any;
}

const props = defineProps<Props>();

// 🎯 使用统一状态管理器
const { state: battleStateSync, getPlayerRoundRecord } = useBattleStateSync()

// 🎯 从父组件注入原始的 localOpenItems 数据
const localOpenItems = inject<Ref<(BattleItem | null)[]>>('localOpenItems', ref([]))

// 🎯 获取玩家ID
const playerId = computed(() => {
  return props.player.uid || props.player.user?.uid || 'unknown'
})

// 🎯 计算实际需要显示的轮次数量
const actualRoundsToShow = computed(() => {
  const { isBattleFinished, isBattleStarted, currentRound } = battleStateSync;

  console.log(`[🎰ROUNDS-GRID] 状态检查:`, {
    isBattleFinished,
    isBattleStarted,
    currentRound,
    totalRounds: props.totalRounds
  });

  if (isBattleFinished) {
    // 对战已结束：显示所有轮次
    console.log(`[🎰ROUNDS-GRID] 对战已结束，显示所有轮次: ${props.totalRounds}`);
    return props.totalRounds;
  } else if (isBattleStarted) {
    // 对战进行中：显示到当前轮次
    const roundsToShow = Math.max(currentRound, 1);
    console.log(`[🎰ROUNDS-GRID] 对战进行中，显示到当前轮次: ${roundsToShow}`);
    return roundsToShow;
  } else {
    // 对战未开始：显示总轮次数（等待状态）
    console.log(`[🎰ROUNDS-GRID] 对战未开始，显示所有轮次: ${props.totalRounds}`);
    return props.totalRounds;
  }
});

// 🎯 计算轮次物品数组
const roundItems = computed(() => {
  const items: (BattleItem | null)[] = [];
  const roundsToShow = actualRoundsToShow.value;

  // 🎯 调试：打印基本信息
  console.log(`[🎰ROUNDS-GRID] 计算轮次物品 - 玩家: ${playerId.value}, 显示轮次: ${roundsToShow}/${props.totalRounds}`)

  for (let roundIndex = 1; roundIndex <= roundsToShow; roundIndex++) {
    const roundIndexZeroBased = roundIndex - 1;
    
    // 1. 优先从统一状态管理器获取
    const syncRecord = getPlayerRoundRecord.value(playerId.value, roundIndexZeroBased);
    if (syncRecord) {
      console.log(`[🎰ROUNDS-GRID] 轮次${roundIndex}: 从状态管理器获取`, syncRecord.name)
      items.push(syncRecord);
      continue;
    }
    
    // 2. 从注入的 localOpenItems 获取（保持索引对应关系）
    if (roundIndexZeroBased < localOpenItems.value.length) {
      const localRecord = localOpenItems.value[roundIndexZeroBased];
      console.log(`[🎰ROUNDS-GRID] 轮次${roundIndex}: 从本地数据获取`, localRecord ? localRecord.name : 'null')
      items.push(localRecord); // 包括 null 值
      continue;
    }
    
    // 3. 最后回退到原始player.open_items
    const openItems = props.player.open_items || [];
    const originalRecord = openItems[roundIndexZeroBased] || null;
    console.log(`[🎰ROUNDS-GRID] 轮次${roundIndex}: 从原始数据获取`, originalRecord ? originalRecord.name : 'null')
    items.push(originalRecord);
  }
  
  console.log(`[🎰ROUNDS-GRID] 最终轮次物品数组:`, {
    length: items.length,
    roundsToShow,
    totalRounds: props.totalRounds,
    items: items.map((item, i) => ({
      round: i + 1,
      name: item ? item.name : 'empty',
      hasItem: !!item
    }))
  });

  return items;
});

// 🎯 调试：监听状态变化
if (import.meta.dev) {
  watch(
    () => battleStateSync.currentRound,
    (newRound, oldRound) => {
      console.log(`[🎰ROUNDS-GRID] 轮次变化 ${oldRound} -> ${newRound}，玩家: ${playerId.value}`)
    }
  )
  
  watch(
    () => getPlayerRoundRecord.value(playerId.value, battleStateSync.currentRound - 1),
    (newRecord, oldRecord) => {
      if (newRecord && !oldRecord) {
        console.log(`[🎰ROUNDS-GRID] 玩家${playerId.value}轮次${battleStateSync.currentRound}新增记录:`, newRecord)
      }
    }
  )

  // 🎯 调试：检查maxPlayers值
  watch(
    () => props.maxPlayers,
    (newValue, oldValue) => {
      console.log(`[🎰ROUNDS-GRID] maxPlayers变化: ${oldValue} -> ${newValue}`)
    },
    { immediate: true }
  )

  // 🎯 调试：检查roundItems计算结果
  watch(
    () => roundItems.value,
    (newItems) => {
      console.log(`[🎰ROUNDS-GRID] 玩家${playerId.value}轮次物品数组:`, newItems.map((item, index) => ({
        round: index + 1,
        item: item ? item.name : 'null',
        source: item ? 'data' : 'empty'
      })))
    },
    { immediate: true }
  )

  // 🎯 调试：检查 localOpenItems 注入
  watch(
    () => localOpenItems.value,
    (items) => {
      console.log(`[🎰ROUNDS-GRID] 玩家${playerId.value}本地开箱物品:`, items.map((item, index) => ({
        round: index + 1,
        item: item ? item.name : 'null'
      })))
    },
    { immediate: true }
  )
}
</script>

 