<template>
  <div class="battle-player-display">
    <!-- 玩家卡片网格 -->
    <div class="players-grid" :data-players="maxPlayers">
      <!-- 已加入的玩家 -->
      <template v-for="(player, index) in players" :key="player.uid ? `player-${player.uid}` : `player-${index}`">
        <BattlePlayerCard
          :player="player"
          :index="index"
          :current-round="currentRound"
          :total-rounds="actualTotalRounds"
          :opening-player-name="openingPlayerName"
          :opening-player-index="openingPlayerIndex"
          :host-uid="hostUid"
          :max-players="maxPlayers"
          :is-battle-started="isBattleStarted"
          :is-battle-finished="isBattleFinished"
          :current-case-detail="currentCaseDetail"
          :current-case-items="currentCaseItems"
          :is-user-creator="isUserCreator"
          :is-user-joined="isUserJoined"
          :current-user-id="currentUserId"
          :is-round-changing="isRoundChanging"
          :player-records="playerRecords"
          :completed-rounds="completedRounds"
          @start-battle="$emit('start-battle')"
          @view-result="$emit('view-result')"
          @leave-battle="$emit('leave-battle')"
          @dismiss-battle="$emit('dismiss-battle')"
          @animation-complete="handleChildAnimationComplete"
        />
        <!-- 🎯 调试：输出轮次信息 -->
        <div v-if="isDev" style="display: none;">
          {{ console.log(`[🎰PLAYER-DISPLAY] 玩家${index}: totalRounds=${actualTotalRounds}, player.open_items.length=${player.open_items?.length || 0}`) }}
        </div>
      </template>

      <!-- 空位卡片 -->
      <template v-for="index in emptySlots" :key="`empty-${index}`">
        <BattleEmptySlot
          :index="index"
          :total-rounds="actualTotalRounds"
          :max-players="maxPlayers"
          :is-battle-started="isBattleStarted"
          :is-user-joined="isUserJoined"
          :is-user-creator="isUserCreator"
          :current-user-id="currentUserId"
          :can-join="canJoin"
          @join-battle="$emit('join-battle')"
        />
      </template>
    </div>

    <!-- 🎨 简化的等待状态提示 -->
    <div v-if="!isBattleStarted" class="waiting-state-container">
      <!-- 主要内容区域 -->
      <div class="waiting-content">
        <!-- 状态图标 -->
        <div class="status-icon-container">
          <div class="status-icon">
            <Icon name="heroicons:clock" class="w-6 h-6 text-blue-400" />
          </div>
        </div>

        <!-- 简化的标题 -->
        <div class="waiting-text">
          <h3 class="waiting-title">
            {{ t("battle.detail.waiting_for_players") }}
          </h3>
        </div>

        <!-- 简化的玩家计数 -->
        <div class="player-count-simple">
          <span class="count-text">{{ players.length }}/{{ maxPlayers }}</span>
          <div class="count-progress">
            <div 
              class="progress-fill" 
              :style="{ width: `${(players.length / maxPlayers) * 100}%` }"
            ></div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { BattleBet } from "~/types/battle";

// 🎯 国际化设置
const { t } = useI18n();

// 🎯 子组件导入
import BattlePlayerCard from "~/components/battle/BattlePlayerCard.vue";
import BattleEmptySlot from "~/components/battle/BattleEmptySlot.vue";
import BattleWinItems from "~/components/battle/BattleWinItems.vue";

// 🎯 Props定义
interface Props {
  players: BattleBet[];
  currentRound: number;
  totalRounds: number;
  openingPlayerName?: string;
  openingPlayerIndex?: number;
  hostUid?: string;
  maxPlayers?: number;
  isBattleStarted?: boolean;
  isBattleFinished?: boolean;
  currentCaseDetail?: any;
  currentCaseItems?: any[];
  currentUserId?: string;
  isUserCreator?: boolean;
  isUserJoined?: boolean;
  // 控制是否允许加入对战
  canJoin?: boolean;
  // 状态同步相关
  isRoundChanging?: boolean;
  playerRecords?: ReadonlyMap<string, readonly any[]>;
  completedRounds?: number[];
  steps?: any[];
}

const props = withDefaults(defineProps<Props>(), {
  openingPlayerName: "",
  openingPlayerIndex: -1,
  hostUid: "",
  maxPlayers: 4,
  isBattleStarted: false,
  isBattleFinished: false,
  currentCaseDetail: () => ({}),
  currentCaseItems: () => [],
  isUserCreator: false,
  isUserJoined: false,
  canJoin: true,
  isRoundChanging: false,
  playerRecords: () => new Map(),
  completedRounds: () => [],
  steps: () => []
});

// 🎯 开发模式检测
const isDev = import.meta.dev

// 🎯 Events
const emit = defineEmits<{
  "join-battle": [];
  "start-battle": [];
  "view-result": [];
  "leave-battle": [];
  "dismiss-battle": [];
  "animation-complete": [playerIndex: number, result: any];
}>();

// 🎯 计算属性
const emptySlots = computed(() => {
  return Math.max(0, props.maxPlayers - props.players.length);
});

// 🎯 智能推断实际轮次数
const actualTotalRounds = computed(() => {
  // 优先使用传入的 totalRounds
  if (props.totalRounds >= 1) {
    console.log(`[🎰PLAYER-DISPLAY] 使用传入的轮次数: ${props.totalRounds}`);
    return props.totalRounds;
  }

  // 如果没有传入有效的轮次数，从玩家数据推断
  let maxRounds = 1;
  for (const player of props.players) {
    if (player.open_items && player.open_items.length > maxRounds) {
      maxRounds = player.open_items.length;
    }
  }

  console.log(`[🎰PLAYER-DISPLAY] 从玩家数据推断轮次数: ${maxRounds}`);
  // 使用推断的轮次数，不强制设置最小值
  return maxRounds;
});

// 🎯 调试：输出轮次推断信息
if (import.meta.dev) {
  watch(() => [props.totalRounds, actualTotalRounds.value], ([original, actual]) => {
    console.log(`[🎰PLAYER-DISPLAY] 轮次推断: 原始=${original}, 推断=${actual}`)
  }, { immediate: true })
}

// 🎯 工具函数
const getWinItems = (player: any): any[] => {
  return player.win_items || [];
};

// 🎯 类型适配函数
const convertToSkinItem = (battleItem: any): any => {
  return {
    id: battleItem.item_id || `battle-${Date.now()}`,
    name: battleItem.name,
    name_zh_hans: battleItem.name_zh_hans,
    name_en: battleItem.name_en,
    image: battleItem.image,
    price: battleItem.item_price?.price || 0,
    item_price: battleItem.item_price,
    item_rarity: battleItem.item_rarity,
    isStatTrak: battleItem.name?.includes("StatTrak™") || false,
    weapon: battleItem.name?.split(" | ")[0] || "",
    skin: battleItem.name?.split(" | ")[1]?.split(" (")[0] || "",
    exterior: battleItem.name?.match(/\(([^)]+)\)$/)?.[1] || "",
  };
};

// 🎯 处理子卡片动画完成事件，兼容旧/新两种参数格式
const handleChildAnimationComplete = (...args: any[]) => {
  // 新版：单个 payload 对象
  if (args.length === 1 && typeof args[0] === 'object') {
    const payload = args[0] as any
    const idx = payload.playerIndex ?? null
    const res = payload.winner ?? payload.result ?? payload
    if (idx !== null) emit('animation-complete', idx, res)
    return
  }
  // 旧版：playerIndex, result
  if (args.length >= 2) {
    emit('animation-complete', args[0], args[1])
  }
}

// ================= 开发模式调试 =================
if (import.meta.dev) {
  // 监听玩家数组长度变化
  watch(
    () => props.players.length,
    (n, o) => console.log('[🎰PLAYER-DISPLAY] players length', { o, n }),
    { immediate: true }
  )

  // 监听玩家 uid 列表变化
  watch(
    () => props.players.map(p => p?.user?.uid || p.uid || 'noUid').join(','),
    (n, o) => console.log('[🎰PLAYER-DISPLAY] uid 列表变化', { o, n }),
    { immediate: true }
  )

  // 🎯 调试：检查maxPlayers值
  watch(
    () => props.maxPlayers,
    (newValue, oldValue) => {
      console.log(`[🎰PLAYER-DISPLAY] maxPlayers变化: ${oldValue} -> ${newValue}`)
    },
    { immediate: true }
  )

  onMounted(() => {
    console.log('[🎰PLAYER-DISPLAY] 组件已挂载，当前 players', props.players)
    console.log('[🎰PLAYER-DISPLAY] 组件已挂载，当前 maxPlayers', props.maxPlayers)
  })
}
</script>

<style lang="scss" scoped>
@use "~/assets/css/components/battle-player-display.scss";
</style>
