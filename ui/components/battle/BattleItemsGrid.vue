<template>
  <div class="items-grid" :data-players="maxPlayers" :data-actual-items="actualItemsCount">
    <div
      v-for="(item, index) in displayItems"
      :key="`${type}-${index}`"
      class="item-wrapper"
      :class="wrapperClass"
    >
      <div v-if="item" class="item-slot">
        <SkinCard
          :skin-item="convertToSkinItem(item)"
          :is-localized="true"
          class="skin-card"
        />
        <!-- 轮次指示器 - 仅在轮次模式下显示，移到右上角 -->
        <div v-if="type === 'rounds'" class="round-indicator-top-right">
          <span class="round-number">{{ getRoundNumber(index) }}</span>
          <div v-if="isCurrentRound(index + 1)" class="current-badge">
            <Icon name="heroicons:play" class="w-3 h-3" />
          </div>
          <div v-else-if="isCompletedRound(index + 1)" class="completed-badge">
            <Icon name="heroicons:check" class="w-3 h-3" />
          </div>
        </div>
      </div>
      <div v-else-if="type === 'rounds'" class="empty-item-slot">
        <Icon
          name="heroicons:minus-circle"
          class="w-6 h-6 text-gray-500"
        />
        <!-- 空轮次指示器 - 移到右上角 -->
        <div class="round-indicator-top-right">
          <span class="round-number">{{ getRoundNumber(index) }}</span>
          <div v-if="isCurrentRound(index + 1)" class="waiting-badge">
            <Icon name="heroicons:clock" class="w-3 h-3" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { BattleItem } from "~/types/battle";

// 🎯 Props定义
interface Props {
  items: (BattleItem | null)[];
  maxPlayers: number;
  convertToSkinItem: (item: BattleItem) => any;
  type: 'win-items' | 'rounds';
  totalRounds?: number; // 仅在轮次模式下需要
  currentRound?: number; // 仅在轮次模式下需要
  completedRounds?: ReadonlySet<number>; // 仅在轮次模式下需要
  isBattleStarted?: boolean; // 仅在轮次模式下需要
  isBattleFinished?: boolean; // 仅在轮次模式下需要
}

const props = withDefaults(defineProps<Props>(), {
  type: 'win-items',
  totalRounds: 0,
  currentRound: 0,
  completedRounds: () => new Set() as ReadonlySet<number>,
  isBattleStarted: false,
  isBattleFinished: false
});

// 🎯 计算属性
const wrapperClass = computed(() => {
  return {
    'win-item-wrapper': props.type === 'win-items',
    'round-slot': props.type === 'rounds'
  };
});

// 🎯 计算实际需要显示的物品数量
const actualItemsCount = computed(() => {
  if (props.type === 'win-items') {
    // 胜利物品模式：显示所有物品
    return props.items.length;
  }

  // 轮次模式：根据对战状态决定显示数量
  if (props.isBattleFinished) {
    // 对战已结束：显示所有轮次
    return props.totalRounds;
  } else if (props.isBattleStarted) {
    // 对战进行中：显示到当前轮次
    return Math.max(props.currentRound, 1);
  } else {
    // 对战未开始：显示总轮次数（等待状态）
    return props.totalRounds;
  }
});

// 🎯 计算要显示的物品数组
const displayItems = computed(() => {
  if (props.type === 'win-items') {
    // 胜利物品模式：显示所有物品
    return props.items;
  }

  // 轮次模式：按需显示
  const count = actualItemsCount.value;
  return props.items.slice(0, count);
});

// 🎯 轮次相关函数 - 仅在轮次模式下使用
const getRoundNumber = (index: number): number => {
  return index + 1;
};

const isCurrentRound = (roundIndex: number): boolean => {
  return props.currentRound === roundIndex && props.isBattleStarted && !props.isBattleFinished;
};

const isCompletedRound = (roundIndex: number): boolean => {
  return props.completedRounds.has(roundIndex) || props.isBattleFinished;
};

// 🎯 调试：检查maxPlayers值
if (import.meta.dev) {
  watch(
    () => props.maxPlayers,
    (newValue, oldValue) => {
      console.log(`[🎰ITEMS-GRID] maxPlayers变化: ${oldValue} -> ${newValue}`)
    },
    { immediate: true }
  )
}
</script>

<style lang="scss" scoped>
// 通用物品网格 - 根据实际物品数量和玩家数量自适应列数
.items-grid {
  display: grid;
  gap: 0.5rem;
  margin-top: 1rem;

  // 🎯 优先根据实际物品数量调整列数
  &[data-actual-items="1"] {
    grid-template-columns: 1fr;
  }

  &[data-actual-items="2"] {
    grid-template-columns: repeat(2, 1fr);
  }

  &[data-actual-items="3"] {
    grid-template-columns: repeat(3, 1fr);
  }

  // 🎯 回退到玩家数量规则（当没有data-actual-items时）
  &[data-players="2"]:not([data-actual-items]) {
    grid-template-columns: repeat(4, 1fr); // 2个玩家显示4列
  }

  &[data-players="3"]:not([data-actual-items]) {
    grid-template-columns: repeat(3, 1fr); // 3个玩家显示3列
  }

  &[data-players="4"]:not([data-actual-items]) {
    grid-template-columns: repeat(2, 1fr); // 4个玩家显示2列
  }

  // 🎯 默认情况（4个或更多物品）
  &[data-actual-items]:not([data-actual-items="1"]):not([data-actual-items="2"]):not([data-actual-items="3"]) {
    // 根据玩家数量决定列数
    &[data-players="2"] {
      grid-template-columns: repeat(4, 1fr);
    }

    &[data-players="3"] {
      grid-template-columns: repeat(3, 1fr);
    }

    &[data-players="4"] {
      grid-template-columns: repeat(2, 1fr);
    }
  }

  // 桌面端保持相同逻辑
  @media (min-width: 768px) {
    // 🎯 优先根据实际物品数量调整列数
    &[data-actual-items="1"] {
      grid-template-columns: 1fr;
    }

    &[data-actual-items="2"] {
      grid-template-columns: repeat(2, 1fr);
    }

    &[data-actual-items="3"] {
      grid-template-columns: repeat(3, 1fr);
    }

    // 🎯 回退到玩家数量规则
    &[data-players="2"]:not([data-actual-items]) {
      grid-template-columns: repeat(4, 1fr);
    }

    &[data-players="3"]:not([data-actual-items]) {
      grid-template-columns: repeat(3, 1fr);
    }

    &[data-players="4"]:not([data-actual-items]) {
      grid-template-columns: repeat(2, 1fr);
    }

    // 🎯 默认情况（4个或更多物品）
    &[data-actual-items]:not([data-actual-items="1"]):not([data-actual-items="2"]):not([data-actual-items="3"]) {
      &[data-players="2"] {
        grid-template-columns: repeat(4, 1fr);
      }

      &[data-players="3"] {
        grid-template-columns: repeat(3, 1fr);
      }

      &[data-players="4"] {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }

  // 大屏幕优化
  @media (min-width: 1024px) {
    // 🎯 优先根据实际物品数量调整列数
    &[data-actual-items="1"] {
      grid-template-columns: 1fr;
    }

    &[data-actual-items="2"] {
      grid-template-columns: repeat(2, 1fr);
    }

    &[data-actual-items="3"] {
      grid-template-columns: repeat(3, 1fr);
    }

    // 🎯 回退到玩家数量规则
    &[data-players="2"]:not([data-actual-items]) {
      grid-template-columns: repeat(4, 1fr);
    }

    &[data-players="3"]:not([data-actual-items]) {
      grid-template-columns: repeat(3, 1fr);
    }

    &[data-players="4"]:not([data-actual-items]) {
      grid-template-columns: repeat(2, 1fr);
    }

    // 🎯 默认情况（4个或更多物品）
    &[data-actual-items]:not([data-actual-items="1"]):not([data-actual-items="2"]):not([data-actual-items="3"]) {
      &[data-players="2"] {
        grid-template-columns: repeat(4, 1fr);
      }

      &[data-players="3"] {
        grid-template-columns: repeat(3, 1fr);
      }

      &[data-players="4"] {
        grid-template-columns: repeat(2, 1fr);
      }
    }
  }
}

// 物品包装器
.item-wrapper {
  position: relative;
  transition: all 0.3s ease;
  min-width: 0; // 允许收缩到最小宽度

  &:hover {
    transform: scale(1.05);
    z-index: 10;
  }
}

// 获胜物品包装器
.win-item-wrapper {
  // 获胜物品特殊样式
}

// 轮次槽位
.round-slot {
  // 轮次特殊样式
}

// 物品槽位
.item-slot {
  position: relative;
}

// 空物品槽位
.empty-item-slot {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 140px;
  background: rgba(255, 255, 255, 0.05);
  border: 2px dashed rgba(255, 255, 255, 0.2);
  border-radius: var(--radius-md, 0.5rem);
  color: rgba(255, 255, 255, 0.5);
}

// 皮肤卡片 - 固定高度，其他信息自适应
.skin-card {
  width: 100%;
  height: 140px;
  border-radius: var(--radius-md, 0.5rem);
  overflow: hidden;
  transition: all 0.3s ease;
  min-width: 0; // 允许收缩

  // 获胜物品特殊样式
  .win-item-wrapper & {
    // border: 2px solid rgba(255, 215, 0, 0.3);
    background: rgba(255, 215, 0, 0.05);

    &:hover {
      border-color: rgba(255, 215, 0, 0.6);
      box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
    }
  }

  // 轮次物品特殊样式
  .round-slot & {
    // border: 2px solid rgba(0, 168, 255, 0.3);
    background: rgba(0, 168, 255, 0.05);

    &:hover {
      border-color: rgba(0, 168, 255, 0.6);
      box-shadow: 0 0 15px rgba(0, 168, 255, 0.3);
    }
  }

  // 确保图片容器固定高度
  :deep(.skin-card) {
    height: 100%;
    min-width: 0; // 允许收缩
    
    .relative {
      height: 100%;
    }
    
    // 图片容器固定高度
    .aspect-\[4\/3\] {
      aspect-ratio: auto;
      height: 80px;
      min-height: 80px;
      max-height: 80px;
    }
    
    // 内容区域自适应
    .p-3 {
      height: calc(100% - 80px); // 剩余高度给内容
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      padding: 0.5rem; // 减小内边距
    }
  }
}

// 轮次指示器 - 右上角版本
.round-indicator-top-right {
  position: absolute;
  top: 0.25rem;
  right: 0.25rem;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: rgba(59, 130, 246, 0.9);
  border-radius: 0.375rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  z-index: 10;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

// 保留原轮次指示器样式（用于其他地方）
.round-indicator {
  position: absolute;
  top: -8px;
  right: -8px;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  background: rgba(0, 0, 0, 0.8);
  border-radius: 0.5rem;
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  z-index: 5;
}

.round-number {
  color: var(--color-text, #ffffff);
}

.current-badge {
  color: var(--color-primary, #00a8ff);
}

.completed-badge {
  color: var(--color-success, #10b981);
}

.waiting-badge {
  color: var(--color-warning, #f59e0b);
}

// 右上角轮次指示器内的元素样式
.round-indicator-top-right {
  .round-number {
    color: rgba(255, 255, 255, 0.95);
  }

  .current-badge {
    color: rgba(255, 255, 255, 0.9);
  }

  .completed-badge {
    color: rgba(34, 197, 94, 1);
  }

  .waiting-badge {
    color: rgba(245, 158, 11, 1);
  }
}
</style> 