#!/bin/bash

# HomeStats监控数据更新 - Docker部署脚本
# 用于部署监控数据实时更新的修改

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查Docker状态
check_docker() {
    log_info "检查Docker服务状态..."
    
    if ! docker info > /dev/null 2>&1; then
        log_error "Docker服务未运行，请启动Docker"
        exit 1
    fi
    
    log_success "Docker服务正常"
}

# 停止现有容器
stop_containers() {
    log_info "停止现有容器..."
    
    # 停止相关容器
    docker-compose down
    
    log_success "容器已停止"
}

# 重新构建镜像
rebuild_images() {
    log_info "重新构建Docker镜像..."
    
    # 清理旧镜像
    docker-compose build --no-cache web thworker-compat
    
    log_success "镜像重新构建完成"
}

# 启动服务
start_services() {
    log_info "启动更新后的服务..."
    
    # 按顺序启动服务
    docker-compose up -d redis
    sleep 5
    
    docker-compose up -d web
    sleep 10
    
    docker-compose up -d celery-worker celery-beat thworker-compat
    sleep 5
    
    docker-compose up -d websocket
    
    log_success "服务启动完成"
}

# 验证监控功能
verify_monitor() {
    log_info "验证监控数据更新功能..."
    
    # 等待服务完全启动
    sleep 20
    
    # 检查Django健康状态
    if curl -f http://localhost:8000/api/health/ > /dev/null 2>&1; then
        log_success "Django服务健康"
    else
        log_error "Django服务未就绪"
        return 1
    fi
    
    # 检查监控API
    if curl -f http://localhost:8000/api/monitor/data/ > /dev/null 2>&1; then
        log_success "监控API正常"
    else
        log_error "监控API异常"
        return 1
    fi
    
    # 检查WebSocket服务
    if curl -f http://localhost:4000/health > /dev/null 2>&1; then
        log_success "WebSocket服务健康"
    else
        log_error "WebSocket服务未就绪"
        return 1
    fi
    
    log_success "监控功能验证通过"
}

# 显示监控日志
show_monitor_logs() {
    log_info "显示监控相关日志..."
    
    echo "=== Django Web服务日志 ==="
    docker-compose logs --tail=20 web | grep -i monitor || true
    
    echo "=== ThWorker服务日志 ==="
    docker-compose logs --tail=20 thworker-compat || true
    
    echo "=== WebSocket服务日志 ==="
    docker-compose logs --tail=20 websocket || true
}

# 测试监控数据
test_monitor_data() {
    log_info "测试监控数据..."
    
    echo "当前监控数据："
    curl -s http://localhost:8000/api/monitor/data/ | grep -o '"stats":{[^}]*}' || true
    
    echo ""
    echo "等待5秒后再次获取数据..."
    sleep 5
    
    echo "5秒后的监控数据："
    curl -s http://localhost:8000/api/monitor/data/ | grep -o '"stats":{[^}]*}' || true
}

# 主函数
main() {
    log_info "开始更新HomeStats监控数据功能"
    
    case "${1:-update}" in
        "update")
            check_docker
            stop_containers
            rebuild_images
            start_services
            verify_monitor
            log_success "监控数据更新部署完成！"
            ;;
        "test")
            test_monitor_data
            ;;
        "logs")
            show_monitor_logs
            ;;
        "verify")
            verify_monitor
            ;;
        "restart")
            check_docker
            stop_containers
            start_services
            verify_monitor
            ;;
        *)
            echo "用法: $0 {update|test|logs|verify|restart}"
            echo "  update  - 完整更新部署（默认）"
            echo "  test    - 测试监控数据"
            echo "  logs    - 显示监控日志"
            echo "  verify  - 验证监控功能"
            echo "  restart - 重启服务"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
