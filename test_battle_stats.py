#!/usr/bin/env python3
"""
测试对战场次统计功能
"""
import requests
import json

def test_battle_stats():
    """测试对战场次统计"""
    base_url = "http://localhost:8000"
    
    # 1. 获取当前统计数据
    print("=== 获取当前统计数据 ===")
    try:
        response = requests.get(f"{base_url}/api/monitor/stats/")
        if response.status_code == 200:
            data = response.json()
            if data.get('code') == 0:
                stats = data.get('body', {})
                current_battle_count = stats.get('battle_number', 0)
                print(f"当前对战场次: {current_battle_count}")
            else:
                print(f"获取统计数据失败: {data.get('message', '未知错误')}")
        else:
            print(f"请求失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"获取统计数据异常: {e}")
    
    # 2. 获取健康检查
    print("\n=== 健康检查 ===")
    try:
        response = requests.get(f"{base_url}/api/health/")
        if response.status_code == 200:
            data = response.json()
            print(f"服务状态: {data.get('status', 'unknown')}")
        else:
            print(f"健康检查失败，状态码: {response.status_code}")
    except Exception as e:
        print(f"健康检查异常: {e}")

if __name__ == "__main__":
    test_battle_stats()
