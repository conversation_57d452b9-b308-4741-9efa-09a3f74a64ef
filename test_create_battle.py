#!/usr/bin/env python3
"""
测试创建对战功能
"""
import subprocess
import json

def test_create_battle():
    """测试创建对战"""
    
    # 1. 获取当前统计数据
    print("=== 获取当前统计数据 ===")
    result = subprocess.run(['curl', '-s', 'http://localhost:8000/api/monitor/data/'], 
                          capture_output=True, text=True)
    
    if result.returncode == 0:
        try:
            data = json.loads(result.stdout)
            if data.get('code') == 0:
                stats = data.get('body', {}).get('stats', {})
                current_battle_count = stats.get('battle_number', 0)
                print(f"当前对战场次: {current_battle_count}")
            else:
                print(f"获取统计数据失败: {data.get('message', '未知错误')}")
        except json.JSONDecodeError as e:
            print(f"解析JSON失败: {e}")
    else:
        print(f"请求失败: {result.stderr}")

if __name__ == "__main__":
    test_create_battle()
