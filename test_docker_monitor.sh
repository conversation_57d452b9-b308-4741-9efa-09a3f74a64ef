#!/bin/bash

# HomeStats监控功能测试脚本
# 用于验证Docker环境中的监控数据实时更新功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查服务状态
check_services() {
    log_info "检查Docker服务状态..."
    
    # 检查容器运行状态
    if ! docker-compose ps | grep -q "Up"; then
        log_error "Docker容器未运行，请先启动服务"
        exit 1
    fi
    
    log_success "Docker容器运行正常"
}

# 测试API连接
test_api_connection() {
    log_info "测试API连接..."
    
    # 测试健康检查
    if curl -f http://localhost:8000/api/health/ > /dev/null 2>&1; then
        log_success "Django API连接正常"
    else
        log_error "Django API连接失败"
        return 1
    fi
    
    # 测试监控API
    if curl -f http://localhost:8000/api/monitor/data/ > /dev/null 2>&1; then
        log_success "监控API连接正常"
    else
        log_error "监控API连接失败"
        return 1
    fi
    
    # 测试WebSocket
    if curl -f http://localhost:4000/health > /dev/null 2>&1; then
        log_success "WebSocket服务连接正常"
    else
        log_error "WebSocket服务连接失败"
        return 1
    fi
}

# 获取监控数据
get_monitor_data() {
    local response=$(curl -s http://localhost:8000/api/monitor/data/)
    echo "$response" | grep -o '"stats":{[^}]*}' | sed 's/"stats"://'
}

# 测试数据变化
test_data_changes() {
    log_info "测试监控数据变化..."
    
    echo "获取初始数据..."
    local initial_data=$(get_monitor_data)
    echo "初始数据: $initial_data"
    
    echo ""
    echo "等待10秒观察数据变化..."
    sleep 10
    
    local second_data=$(get_monitor_data)
    echo "10秒后数据: $second_data"
    
    echo ""
    echo "再等待10秒..."
    sleep 10
    
    local third_data=$(get_monitor_data)
    echo "20秒后数据: $third_data"
    
    # 检查在线人数是否有变化
    local initial_online=$(echo "$initial_data" | grep -o '"online_number":[0-9]*' | cut -d':' -f2)
    local second_online=$(echo "$second_data" | grep -o '"online_number":[0-9]*' | cut -d':' -f2)
    local third_online=$(echo "$third_data" | grep -o '"online_number":[0-9]*' | cut -d':' -f2)
    
    echo ""
    echo "在线人数变化: $initial_online → $second_online → $third_online"
    
    if [ "$initial_online" != "$second_online" ] || [ "$second_online" != "$third_online" ]; then
        log_success "在线人数正在动态变化 ✓"
    else
        log_warning "在线人数没有变化，可能监控线程未运行"
    fi
}

# 检查监控线程
check_monitor_thread() {
    log_info "检查监控线程状态..."
    
    # 检查Django日志中的监控线程启动信息
    local monitor_logs=$(docker-compose logs web 2>/dev/null | grep -i "monitor.*启动" || true)
    
    if [ -n "$monitor_logs" ]; then
        log_success "监控线程已启动"
        echo "$monitor_logs"
    else
        log_warning "未找到监控线程启动日志"
    fi
    
    # 检查ThWorker服务
    local thworker_status=$(docker-compose ps thworker-compat 2>/dev/null | grep "Up" || true)
    
    if [ -n "$thworker_status" ]; then
        log_success "ThWorker服务运行正常"
    else
        log_warning "ThWorker服务可能未运行"
    fi
}

# 检查Redis连接
check_redis() {
    log_info "检查Redis连接..."
    
    if docker exec csgoskins-redis redis-cli ping > /dev/null 2>&1; then
        log_success "Redis连接正常"
    else
        log_error "Redis连接失败"
        return 1
    fi
    
    # 检查Redis中的监控数据
    local redis_keys=$(docker exec csgoskins-redis redis-cli keys "*monitor*" 2>/dev/null || true)
    if [ -n "$redis_keys" ]; then
        log_info "Redis中的监控相关键: $redis_keys"
    fi
}

# 显示详细日志
show_detailed_logs() {
    log_info "显示详细服务日志..."
    
    echo "=== Django Web服务日志（最近20行）==="
    docker-compose logs --tail=20 web
    
    echo ""
    echo "=== ThWorker服务日志（最近20行）==="
    docker-compose logs --tail=20 thworker-compat
    
    echo ""
    echo "=== WebSocket服务日志（最近20行）==="
    docker-compose logs --tail=20 websocket
    
    echo ""
    echo "=== Redis服务日志（最近10行）==="
    docker-compose logs --tail=10 redis
}

# 性能测试
performance_test() {
    log_info "进行性能测试..."
    
    echo "连续获取10次监控数据，测试响应时间..."
    
    for i in {1..10}; do
        local start_time=$(date +%s%N)
        curl -s http://localhost:8000/api/monitor/data/ > /dev/null
        local end_time=$(date +%s%N)
        local duration=$(( (end_time - start_time) / 1000000 ))
        echo "第${i}次请求: ${duration}ms"
        sleep 1
    done
}

# 主函数
main() {
    log_info "开始HomeStats监控功能测试"
    
    case "${1:-full}" in
        "full")
            check_services
            test_api_connection
            check_monitor_thread
            check_redis
            test_data_changes
            log_success "完整测试完成"
            ;;
        "quick")
            check_services
            test_api_connection
            test_data_changes
            ;;
        "logs")
            show_detailed_logs
            ;;
        "performance")
            performance_test
            ;;
        "data")
            echo "当前监控数据:"
            get_monitor_data
            ;;
        *)
            echo "用法: $0 {full|quick|logs|performance|data}"
            echo "  full        - 完整测试（默认）"
            echo "  quick       - 快速测试"
            echo "  logs        - 显示详细日志"
            echo "  performance - 性能测试"
            echo "  data        - 获取当前数据"
            exit 1
            ;;
    esac
}

# 执行主函数
main "$@"
