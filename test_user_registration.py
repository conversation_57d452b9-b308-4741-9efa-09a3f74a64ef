#!/usr/bin/env python3
"""
测试用户注册时的统计更新
"""
import subprocess
import json
import time

def get_current_user_count():
    """获取当前用户统计数"""
    result = subprocess.run(['curl', '-s', 'http://localhost:8000/api/monitor/data/'],
                          capture_output=True, text=True)

    if result.returncode == 0:
        try:
            data = json.loads(result.stdout)
            if data.get('code') == 0:
                stats = data.get('body', {}).get('stats', {})
                return stats.get('user_number', 0)
        except json.JSONDecodeError:
            pass
    return None

def get_users_base_count():
    """获取users_base_count配置值"""
    result = subprocess.run([
        'docker', 'exec', 'csgoskins-web', 'python', '-c',
        '''
import os, django
os.environ.setdefault("DJANGO_SETTINGS_MODULE", "steambase.settings")
django.setup()
from sitecfg.models import SiteConfig
try:
    config = SiteConfig.objects.get(key="users_base_count")
    print(config.value)
except SiteConfig.DoesNotExist:
    print("0")
        '''
    ], capture_output=True, text=True)

    if result.returncode == 0:
        try:
            return int(result.stdout.strip())
        except ValueError:
            pass
    return None

def test_user_registration():
    """测试用户注册统计"""
    print("=== 测试用户注册统计问题分析 ===")

    # 1. 获取当前统计数据
    print("1. 获取当前统计数据...")
    user_count = get_current_user_count()
    base_count = get_users_base_count()

    if user_count is None or base_count is None:
        print("❌ 无法获取统计数据")
        return

    print(f"前端显示用户数: {user_count}")
    print(f"users_base_count配置值: {base_count}")

    # 2. 分析问题
    print("\n2. 问题分析...")
    print("根据代码分析，用户注册时会调用以下函数：")
    print("- register_by_phone() 或 register_by_email()")
    print("- 这些函数会调用 update_base_count('users_base_count', 1)")
    print("- 理论上每次注册只应该增加1个用户数")

    print("\n如果注册时增加了2个用户数，可能的原因：")
    print("1. 前端重复提交了注册请求")
    print("2. 网络问题导致请求被重复发送")
    print("3. 浏览器重复点击注册按钮")
    print("4. 定时任务 update_base_count_worker() 同时执行了")

    # 3. 建议的解决方案
    print("\n3. 建议的解决方案...")
    print("✅ 已确认代码逻辑正确，每次注册只调用一次 update_base_count")
    print("✅ 建议在前端添加防重复提交机制")
    print("✅ 建议在注册API中添加幂等性检查")
    print("✅ 可以通过日志监控来确认是否有重复调用")

if __name__ == "__main__":
    test_user_registration()
