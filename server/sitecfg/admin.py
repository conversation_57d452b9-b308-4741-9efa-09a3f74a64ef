import json
import logging
import os

from django.db.models import Q  # 添加导入语句
from django.contrib import admin, messages
from django.contrib.admin import TabularInline, StackedInline
from django.utils.translation import gettext_lazy as _
from django.db import models
from django import forms



# # from jet.filters import DateRangeFilter  # 暂时禁用，等待Django 4.x兼容版本  # 暂时禁用，等待Django 4.x兼容版本
from modeltranslation.admin import TranslationAdmin

from steambase.admin import ReadOnlyAdmin, ChangeOnlyAdmin

from sitecfg.models import SiteConfig, Support, Footer, FAQ, Banner, SEO, AccessControl
# Announcee, Article, ArticleCategoryy 已迁移到 articles 应用
from sitecfg.business import setup_set_item_price_worker, setup_set_user_box_chance_worker
from sitecfg.service.admin_actions import startup_set_online_base_count

_logger = logging.getLogger(__name__)


@admin.register(SiteConfig)
class SiteConfigAdmin(admin.ModelAdmin):
    fields = ('remark', 'key', 'value', 'enable')
    list_display = ('remark', 'key', 'value', 'enable')
    list_editable = ('enable', 'value')
    list_per_page = 100
    #actions = [startup_set_online_base_count]

    def save_model(self, request, obj, form, change):
        obj.save()
        if obj.key == 'price_api_chosen':
            setup_set_item_price_worker()
        if obj.key == 'box_chance_chosen':
            setup_set_user_box_chance_worker(obj.value)

    def get_queryset(self, request):
        # 获取原始查询集
        queryset = super().get_queryset(request)

        # 根据特定条件过滤数据，这里假设要隐藏 key 为 'recharge_box_chance_type' 的数据
        # queryset = queryset.exclude(
        #      Q(key='recharge_box_chance_type') |
        #      Q(key='register_box_chance_type') |
        #      Q(key='luckybox_rate_type_a') | Q(key='luckybox_rate_type_b') | Q(key='luckybox_rate_type_c') | Q(key='luckybox_rate_type_d') | Q(key='luckybox_rate_type_e')  | 
        #      Q(key='luckybox_rate')  | Q(key='maintenance_luckybox')  | Q(key='online_users_base_count') | Q(key='pk_bot_num_max')  | Q(key='box_bot_num_max')  | Q(key='pk_base_count') | 
        #      Q(key='pk_bot_join_interval_time') | Q(key='pk_bot_interval_time') | Q(key='roll_bet_join') | Q(key='msg_mobile')  | Q(key='enable_dynamic_box_chance') | 
        #      Q(key='open_base_count') | Q(key='users_base_count') | Q(key='enable_recharge_bot') | Q(key='HKD_exchange_rate	') | Q(key='Charge_USD_exchange_rate')

        # )
        return queryset


# AnnounceeAdmin 已迁移到 articles 应用
# @admin.register(Announcee)
# class AnnounceeAdmin(admin.ModelAdmin):
#     fields = ('remark', 'type', 'content', 'enable')
#     list_display = ('remark', 'type', 'enable')
#     list_editable = ('enable',)
#     list_filter = ('type',)
#     list_per_page = 50



@admin.register(Support)
class SupportAdmin(admin.ModelAdmin):
    fields = ('remark', 'title', 'content', 'sort', 'enable')
    list_display = ('sort', 'title', 'remark', 'enable')
    list_editable = ('enable', )
    list_per_page = 50




@admin.register(FAQ)
class FAQAdmin(admin.ModelAdmin):
    fields = ('title', 'content', 'enable' )
    list_display = ('title', 'enable')
    list_editable = ('enable', )
    list_per_page = 20


@admin.register(Banner)
class BannerAdmin(admin.ModelAdmin):
    list_display = ('title', 'type', 'is_simple', 'enable', 'order')
    list_editable = ('enable', 'order')


# ArticleAdmin 已迁移到 articles 应用
# @admin.register(Article)
# class ArticleAdmin(admin.ModelAdmin):
#     #fields = ('title', 'content', 'tag', 'color','image' 'enable')
#     list_display = ('title', 'category', 'tag', 'enable')
#     list_filter = ('category',)
#     search_fields = ('title', 'content', 'tag', 'image')
#     list_editable = ('enable', )
#     list_per_page = 100

@admin.register(SEO)
class SEOAdmin(admin.ModelAdmin):
    fields = ('name', 'subtitle', 'url', 'title', 'keywords', 'description')
    list_display = ('name', 'subtitle', 'url', 'title', 'keywords', 'description')
    formfield_overrides = {
        models.CharField: {'widget': forms.TextInput(attrs={'style': 'width: 60rem;'})},
        models.TextField: {'widget': forms.Textarea(attrs={'rows': 4, 'cols': 80})},

    }


@admin.register(AccessControl)
class AccessControlAdmin(admin.ModelAdmin):
    list_display = ('ip', 'message', 'enable')
    list_editable = ('enable', 'message')
    list_display_links = ('ip',)  # 设置 'ip' 字段为链接字段

    list_per_page = 100

# ArticleCategoryyAdmin 已迁移到 articles 应用
# @admin.register(ArticleCategoryy)
# class ArticleCategoryyAdmin(admin.ModelAdmin):
#     list_display = ('name', 'enable')
#     list_editable = ('enable', )