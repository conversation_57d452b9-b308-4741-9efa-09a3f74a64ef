import logging
import time
import json
import random
import math
from datetime import timedelta, datetime
from django.utils import timezone
from django.conf import settings
from django_redis import get_redis_connection
import pytz

from steambase.enums import RespCode, GameState
from steambase.redis_con import get_redis
from django.core.cache import cache
from authentication.interfaces import get_user_statistics_day_data, get_users_number_data
from box.interfaces import get_case_statistics_day_data, get_case_number_data, get_case_records_data, get_case_item_info_number_data
from sitecfg.interfaces import get_online_users_base_count, get_users_base_count, get_open_base_count, get_pk_base_count
from box.business import get_total_open_count

_logger = logging.getLogger(__name__)

_box_redis_channel_key = 'ws_channel'

def ws_send_monitor(data, action, channel='monitor'):
    if data:
        r = get_redis_connection('default')
        rt_msg = [channel, action, data]
        r.publish(_box_redis_channel_key, json.dumps(rt_msg))

def get_online_number():
    r1 = get_redis()
    return r1.dbsize()

def get_monitor_data(num):
    # case_records = get_case_records_data(num)
    case_records = cache.get('case_records')
    stats = generate_monitor_stats()
    
    resp = {
        'stats': stats,
        'case_records': case_records
    }
    return RespCode.Succeed.value, resp

class RealisticMonitorSystem:
    """真实感监控系统"""
    
    def __init__(self):
        self.last_online_count = 0
        self.trend_direction = 0  # -1: 下降, 0: 稳定, 1: 上升
        self.trend_strength = 0.1  # 趋势强度
        self.volatility = 0.05  # 波动性
        self.activity_events = []  # 活动事件历史
        self.peak_hours = [10, 14, 19, 21]  # 高峰时段
        self.low_hours = [2, 4, 6]  # 低谷时段
        
    def calculate_realistic_online_count(self):
        """计算真实感的在线人数 - 简化版本，更有变化"""
        base_count = get_online_users_base_count()
        if base_count <= 0:
            base_count = 1000  # 默认基础人数

        # 1. 时间段系数（简化版）
        time_multiplier = self.get_simple_time_multiplier()

        # 2. 随机波动（增加变化幅度）
        random_factor = random.uniform(0.7, 1.4)  # 30%的随机波动

        # 3. 周期性变化（模拟用户活跃度的自然波动）
        cycle_factor = self.get_cycle_factor()

        # 4. 突发事件（增加戏剧性变化）
        spike_factor = self.get_spike_factor()

        # 5. 计算最终在线人数
        final_online = int(base_count * time_multiplier * random_factor * cycle_factor * spike_factor)

        # 设置合理的上下限
        min_online = max(20, int(base_count * 0.3))  # 最少30%的基础人数
        max_online = int(base_count * 2.5)  # 最多2.5倍基础人数
        final_online = max(min_online, min(final_online, max_online))

        # 平滑处理（减少平滑限制，允许更大变化）
        if self.last_online_count > 0:
            max_change = max(10, int(self.last_online_count * 0.25))  # 每次最多变动25%或10人
            diff = final_online - self.last_online_count
            if abs(diff) > max_change:
                final_online = self.last_online_count + (max_change if diff > 0 else -max_change)

        self.last_online_count = final_online
        return final_online
    
    def get_enhanced_time_period(self):
        """增强的时间段系数计算"""
        beijing_tz = pytz.timezone('Asia/Shanghai')
        now = timezone.now().astimezone(beijing_tz)
        hour = now.hour
        minute = now.minute
        
        # 更精细的时间段划分
        time_periods = {
            (0, 2): 0.8,    # 深夜低谷
            (2, 4): 0.6,    # 凌晨最低
            (4, 6): 0.7,    # 清晨
            (6, 8): 0.9,    # 早高峰前
            (8, 10): 1.4,   # 上午高峰
            (10, 12): 1.6,  # 上午活跃
            (12, 14): 1.5,  # 午休时间
            (14, 16): 1.7,  # 下午高峰
            (16, 18): 1.8,  # 下午活跃
            (18, 20): 1.9,  # 晚高峰
            (20, 22): 2.1,  # 晚间活跃
            (22, 24): 1.2   # 夜间
        }
        
        # 查找对应时间段
        for (start_hour, end_hour), multiplier in time_periods.items():
            if start_hour <= hour < end_hour:
                # 在时间段内进行微调
                minute_factor = minute / 60.0
                if hour in self.peak_hours:
                    # 高峰时段，在中间时间达到峰值
                    peak_adjustment = math.sin(minute_factor * math.pi) * 0.2
                    return multiplier + peak_adjustment
                elif hour in self.low_hours:
                    # 低谷时段，波动较小
                    return multiplier + random.uniform(-0.1, 0.1)
                else:
                    # 普通时段，自然波动
                    return multiplier + random.uniform(-0.15, 0.15)
        
        return 1.0
    
    def calculate_trend_adjustment(self):
        """计算趋势调整"""
        # 趋势会随时间自然变化
        if random.random() < 0.1:  # 10%概率改变趋势
            self.trend_direction = random.choice([-1, 0, 1])
            self.trend_strength = random.uniform(0.05, 0.2)
        
        # 趋势强度会逐渐减弱
        self.trend_strength *= 0.95
        
        return self.trend_direction * self.trend_strength
    
    def calculate_random_variation(self):
        """计算随机波动"""
        # 基于正态分布的随机波动
        variation = random.gauss(0, self.volatility * 100)
        
        # 添加突发事件的概率
        if random.random() < 0.02:  # 2%概率出现突发事件
            variation += random.uniform(50, 200)
        
        return variation
    
    def calculate_event_boost(self):
        """计算活动事件加成"""
        current_time = time.time()
        
        # 清理过期的活动事件
        self.activity_events = [
            event for event in self.activity_events 
            if current_time - event['timestamp'] < 3600  # 1小时内的事件
        ]
        
        # 计算当前活动加成
        total_boost = 0
        for event in self.activity_events:
            time_passed = current_time - event['timestamp']
            # 活动效果随时间衰减
            decay_factor = max(0, 1 - time_passed / 3600)
            total_boost += event['boost'] * decay_factor
        
        return total_boost
    
    def add_activity_event(self, event_type, boost=0.1):
        """添加活动事件"""
        event = {
            'type': event_type,
            'boost': boost,
            'timestamp': time.time()
        }
        self.activity_events.append(event)
        
        # 限制事件数量
        if len(self.activity_events) > 10:
            self.activity_events.pop(0)
    
    def update_trend(self, current_count):
        """更新趋势"""
        if self.last_online_count > 0:
            change_ratio = (current_count - self.last_online_count) / self.last_online_count
            
            if abs(change_ratio) > 0.05:  # 变化超过5%才更新趋势
                if change_ratio > 0:
                    self.trend_direction = 1
                else:
                    self.trend_direction = -1
                self.trend_strength = min(0.3, abs(change_ratio))
        
        self.last_online_count = current_count

# 全局监控系统实例
monitor_system = RealisticMonitorSystem()

def update_online_number():
    """更新在线人数 - 优化版本"""
    while True:
        try:
            # 使用真实感监控系统计算在线人数
            online_number = monitor_system.calculate_realistic_online_count()
            
            # 添加一些随机活动事件
            if random.random() < 0.01:  # 1%概率触发活动事件
                event_types = ['special_case', 'battle_event', 'promotion']
                event_type = random.choice(event_types)
                boost = random.uniform(0.05, 0.15)
                monitor_system.add_activity_event(event_type, boost)
                _logger.info(f'触发活动事件: {event_type}, 加成: {boost:.2f}')
            
            _logger.info(f'更新在线人数: {online_number}')
            
            # 发送WebSocket消息
            ws_send_monitor(online_number, 'update', 'online_number')
            
        except Exception as e:
            _logger.error(f'更新在线人数错误: {e}')
        finally:
            # 动态调整更新频率
            update_interval = random.uniform(8, 12)  # 8-12秒随机间隔
            time.sleep(update_interval)

def update_monitor_data():
    """更新监控数据 - 只更新在线人数"""
    while True:
        try:
            # 只更新在线人数，其他数据保持不变
            online_number = monitor_system.calculate_realistic_online_count()

            # 只推送在线人数更新
            ws_send_monitor(online_number, 'update', 'online_number')
            _logger.debug(f'更新在线人数: {online_number}')

        except Exception as e:
            _logger.error(f'更新在线人数错误: {e}')
        finally:
            # 动态调整更新频率
            update_interval = random.uniform(8, 12)  # 8-12秒随机间隔
            time.sleep(update_interval)

def get_time_period(timestamp):
    """获取时间段系数 - 保持向后兼容"""
    beijing_tz = pytz.timezone('Asia/Shanghai')
    t = time.gmtime(timestamp - 8*60*60)  # 转换为UTC时间

    hour = t.tm_hour
    minute = t.tm_min

    time_periods = {
        (0, 2): 2.5,
        (2, 4): 2,
        (4, 6): 1.3,
        (6, 8): 1.2,
        (8, 10): 1.5,
        (10, 12): 2.2,
        (12, 14): 2,
        (14, 16): 2.2,
        (16, 18): 2.5,
        (18, 20): 1.8,
        (20, 22): 1.5,
        (22, 24): 2
    }

    for (start_hour, end_hour), value in time_periods.items():
        if start_hour <= hour < end_hour:
            return value
    return 2

def generate_monitor_stats():
    """生成监控统计数据 - 使用实际数据和基数的混合"""
    # 获取基数配置
    users_base = get_users_base_count()
    online_users_base_count = get_online_users_base_count()
    open_base = get_open_base_count()
    pk_base = get_pk_base_count()

    # 获取实际统计数据
    try:
        actual_users = get_users_number_data()  # 实际用户数
        actual_opens = get_total_open_count()   # 实际开箱总数

        # 使用实际数据 + 基数的方式
        users_number = users_base + actual_users
        open_count = open_base + actual_opens
        pk_number = pk_base  # 对战数据暂时使用基数

    except Exception as e:
        _logger.warning(f'获取实际统计数据失败，使用基数: {e}')
        # 如果获取实际数据失败，回退到基数
        users_number = users_base
        open_count = open_base
        pk_number = pk_base

    # 计算在线人数（使用真实感系统）
    online_number = monitor_system.calculate_realistic_online_count()

    stats = {
        'user_number': users_number,
        'online_number': online_number,
        'case_number': open_count,
        'battle_number': pk_number,
    }
    return stats

def generate_enhanced_monitor_stats():
    """生成增强的监控统计数据 - 使用实际数据"""
    # 获取基数配置
    users_base = get_users_base_count()
    open_base = get_open_base_count()
    pk_base = get_pk_base_count()

    # 获取实际统计数据
    try:
        actual_users = get_users_number_data()  # 实际用户数
        actual_opens = get_total_open_count()   # 实际开箱总数

        # 使用实际数据 + 基数的方式
        users_number = users_base + actual_users
        open_count = open_base + actual_opens
        pk_number = pk_base  # 对战数据暂时使用基数

    except Exception as e:
        _logger.warning(f'获取实际统计数据失败，使用基数: {e}')
        # 如果获取实际数据失败，回退到基数
        users_number = users_base
        open_count = open_base
        pk_number = pk_base

    # 使用真实感监控系统计算在线人数
    online_number = monitor_system.calculate_realistic_online_count()

    # 添加实时活动数据
    current_time = timezone.now()
    recent_opens = get_recent_activity_count('open', minutes=5)
    recent_battles = get_recent_activity_count('battle', minutes=5)

    # 计算活跃度指标
    activity_level = calculate_activity_level(recent_opens, recent_battles)

    stats = {
        'user_number': users_number,
        'online_number': online_number,
        'case_number': open_count,
        'battle_number': pk_number,
        'recent_activity': {
            'opens_last_5min': recent_opens,
            'battles_last_5min': recent_battles,
            'activity_level': activity_level
        },
        'trend_info': {
            'direction': monitor_system.trend_direction,
            'strength': monitor_system.trend_strength,
            'volatility': monitor_system.volatility
        },
        'timestamp': current_time.isoformat()
    }
    return stats

def get_recent_activity_count(activity_type, minutes=5):
    """获取最近活动数量"""
    try:
        # 这里可以根据实际需求实现
        # 例如从Redis缓存或数据库查询最近的活动记录
        if activity_type == 'open':
            # 模拟最近5分钟的开箱数量
            base_count = random.randint(10, 50)
            time_factor = monitor_system.get_enhanced_time_period()
            return int(base_count * time_factor)
        elif activity_type == 'battle':
            # 模拟最近5分钟的对战数量
            base_count = random.randint(5, 20)
            time_factor = monitor_system.get_enhanced_time_period()
            return int(base_count * time_factor)
    except Exception as e:
        _logger.warning(f'获取活动数量失败: {e}')
    
    return 0

def calculate_activity_level(opens, battles):
    """计算活跃度等级"""
    total_activity = opens + battles * 2  # 对战权重更高
    
    if total_activity > 100:
        return 'very_high'
    elif total_activity > 60:
        return 'high'
    elif total_activity > 30:
        return 'medium'
    elif total_activity > 10:
        return 'low'
    else:
        return 'very_low'

# 添加一些辅助函数用于触发活动事件
def trigger_special_event(event_type='special_case', boost=0.1):
    """触发特殊事件"""
    monitor_system.add_activity_event(event_type, boost)
    _logger.info(f'手动触发活动事件: {event_type}, 加成: {boost}')

def get_monitor_system_status():
    """获取监控系统状态"""
    return {
        'last_online_count': monitor_system.last_online_count,
        'trend_direction': monitor_system.trend_direction,
        'trend_strength': monitor_system.trend_strength,
        'volatility': monitor_system.volatility,
        'active_events': len(monitor_system.activity_events),
        'current_time': timezone.now().isoformat()
    }