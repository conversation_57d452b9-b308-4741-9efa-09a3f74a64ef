from django.apps import AppConfig
from django.utils.translation import gettext_lazy as _
import threading
import logging

logger = logging.getLogger(__name__)

class MonitorConfig(AppConfig):
    name = 'monitor'
    verbose_name = _('Monitor')

    def ready(self):
        """应用启动时的初始化"""
        # 只在主进程中启动监控线程，避免在开发模式下重复启动
        import os
        if os.environ.get('RUN_MAIN') != 'true':
            return

        try:
            from monitor.interfaces import setup_update_monitor_data
            logger.info('[Monitor] 启动监控数据更新线程...')
            setup_update_monitor_data()
            logger.info('[Monitor] 监控数据更新线程已启动')
        except Exception as e:
            logger.error(f'[Monitor] 启动监控线程失败: {e}')
