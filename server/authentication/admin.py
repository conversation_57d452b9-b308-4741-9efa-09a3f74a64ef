from django.conf import settings
from django.contrib import admin
from django.contrib.admin import ModelAdmin, TabularInline, StackedInline
from django.contrib.admin.views.main import ChangeList
from django.contrib.auth import get_user_model
from django.contrib.auth.admin import UserAdmin
from django.contrib.auth.hashers import make_password
from django.db import transaction, models
from django.db.models import F, Sum, Case, When
from django.utils.translation import gettext_lazy as _
from django.contrib.admin.models import LogEntry

# # # from jet.filters import DateRangeFilter  # 暂时禁用，等待Django 4.x兼容版本  # 暂时禁用，等待Django 4.x兼容版本  # 暂时禁用，等待Django 4.x兼容版本

from steambase.admin import ReadOnlyAdmin
from steambase.enums import ChargeState

from authentication.models import UserProfile, UserAsset, UserSteam, UserExtra, AuthUser
from authentication.models import UserBalanceRecord, User<PERSON>ointsR<PERSON>ord, UserStatisticsDay, PhoneCodeRecord
# from charge.interfaces import get_charge_total
from sitecfg.interfaces import get_user_point_max
from authentication.service.admin_actions import change_user_chance_a_b, userbalance_export_to_excel, \
    authuser_export_to_excel

USER = get_user_model()


class UserProfileInline(StackedInline):
    model = UserProfile
    can_delete = False
    fields = ['avatar']
    # readonly_fields = ['avatar']


class UserAssetInline(StackedInline):
    model = UserAsset
    can_delete = False
    fields = ['tradeurl', 'balance', 'total_charge_balance']
    # readonly_fields = ['tradeurl']


class UserSteamInline(StackedInline):
    model = UserSteam
    can_delete = False
    fields = ['steamid', 'personaname', 'profileurl', 'avatar', 'avatarmedium', 'avatarfull', 'level',
              'own_games_count', 'dota2_playtime']
    readonly_fields = ['steamid', 'personaname', 'profileurl', 'level',
                       'own_games_count', 'dota2_playtime']


class UserExtraInline(StackedInline):
    model = UserExtra
    can_delete = False
    fields = ['box_chance_type', 'ban_chat', 'ban_withdraw', 'ban_withdraw_reason', 'ban_create_roll_room', 'luckybox_rate_type',
              'box_freegive_count',
              ('freebox_lv1_count', 'freebox_lv2_count'), ('freebox_lv3_count', 'freebox_lv4_count'),
              ('freebox_lv5_count','freebox_lv6_count'), ('freebox_lv7_count', 'freebox_lv8_count'),
              ('freebox_lv9_count', 'freebox_lv10_count'),('freebox_lv1_limit', 'freebox_lv2_limit'),
              ('freebox_lv3_limit', 'freebox_lv4_limit'), ('freebox_lv5_limit','freebox_lv6_limit'),
              ('freebox_lv7_limit', 'freebox_lv8_limit'), ('freebox_lv9_limit', 'freebox_lv10_limit'),
              'promotion_box_level']
    readonly_fields = ['box_free_last']


@admin.register(UserBalanceRecord)
class UserBalanceRecordAdmin(ReadOnlyAdmin):
    fields = ('user', 'balance_changed', 'balance_before', 'balance_after', 'reason', 'create_time')
    list_display = ('user', 'balance_changed', 'balance_before', 'balance_after', 'reason', 'create_time')
    list_filter = ('reason',)  # # 暂时禁用 暂时禁用
    search_fields = ('user__username',)
    list_per_page = 50

    def has_delete_permission(self, request, obj=None):
        # 允许删除用户余额记录（主要用于级联删除用户时）
        return request.user.is_superuser
    actions = [userbalance_export_to_excel]

    def has_change_permission(self, request, obj=None):
        return True


@admin.register(AuthUser)
class AuthUserAdmin(UserAdmin):
    fieldsets = (
        (None, {'fields': ('username', 'phone', 'password')}),
        (_('Permissions'), {'fields': ('is_active', 'ban_active_reasons', 'is_staff', 'is_superuser')}),
        (_('Important dates'), {'fields': ('last_login', 'date_joined')}),
    )
    # readonly_fields = ('phone', 'last_login', 'date_joined')
    readonly_fields = ('last_login', 'date_joined', 'balance', 'total_charge_balance')
    list_display = ('username', 'personname', 'balance', 'is_active', 'date_joined')
    # list_filter = (
    #     'is_staff', 'is_superuser', 'is_active', 'groups', 'extra__box_chance_type', # ('date_joined', DateRangeFilter) 暂时禁用)
    search_fields = ('username', 'email', 'phone')
    inlines = [UserProfileInline, UserAssetInline, UserSteamInline]
    #actions = [change_user_chance_a_b, authuser_export_to_excel]
    ordering = ('-date_joined',)  # 按 date_joined 降序排列


    # def changelist_view(self, request, extra_context=None):
    #     post = request.POST.copy()
    #     action = post.get('action')
    #     if action:
    #         if action == 'change_user_chance_a_b' and not post.get('_selected_action'):
    #             self_obj = self.model.objects
    #             if UserExtra.objects.filter(box_chance_type='a'):
    #                 post_list = [item.id for item in self_obj.filter(extra__box_chance_type='a')]
    #                 post.setlist('box_chance_type', ['b'])
    #             else:
    #                 post_list = [item.id for item in self_obj.filter(extra__box_chance_type='b')]
    #                 post.setlist('box_chance_type', ['a'])
    #             post.setlist('_selected_action', post_list)
    #             request.POST = post
    #     return super(AuthUserAdmin, self).changelist_view(request, extra_context)

    # def get_queryset(self, request):
    #     qs = super().get_queryset(request)
    #     qs = qs.annotate(charge=Sum(
    #         Case(
    #             When(charges__state=ChargeState.Succeed.value, then=F('charges__amount')),
    #             default=0,
    #             output_field=models.IntegerField()
    #         )
    #     ))
    #     return qs

    def balance(self, obj):
        # 假设这里计算或获取balance值
        return obj.asset.balance
    balance.short_description = _('余额')

    def total_charge_balance(self, obj):
        # 假设这里计算或获取total_charge_balance值
        return obj.some_method_to_get_total_charge_balance()
    total_charge_balance.short_description = _('总充值余额')
    def personname(self, obj):
        return obj.steam.personaname

    personname.short_description = _('昵称')
    personname.admin_order_field = 'steam__personaname'

    # def charge_total(self, obj):
    #     # return get_charge_total(obj.uid)
    #     return obj.charge
    # charge_total.short_description = 'charge_total'
    # charge_total.admin_order_field = 'charge'

    def points(self, obj):
        return obj.asset.points

    points.short_description = 'points'

    def box_chance_type(self, obj):
        return obj.extra.box_chance_type

    box_chance_type.short_description = _('box_chance_type')

    

    def save_model(self, request, obj, form, change):
        is_exists = UserAsset.objects.filter(user=obj).first()
        flag = False
        if is_exists:
            flag = True
        if flag:
            with transaction.atomic():
                asset = UserAsset.objects.select_for_update().get(user=obj)
                if obj.asset.balance < 0:
                    obj.asset.balance = 0
                if obj.asset.points < 0:
                    obj.asset.points = 0
                if obj.asset.points > get_user_point_max():
                    obj.asset.points = get_user_point_max()
                balance_before = asset.balance
                balance_after = obj.asset.balance
                balance_changed = balance_after - asset.balance
                points_before = asset.points
                points_after = obj.asset.points
                points_changed = points_after - points_before
                if balance_changed:
                    UserBalanceRecord.objects.create(
                        user=obj,
                        balance_changed=balance_changed,
                        balance_before=balance_before,
                        balance_after=balance_after,
                        reason='管理员修改'
                    )
                if points_changed:
                    UserPointsRecord.objects.create(
                        user=obj,
                        points_changed=points_changed,
                        points_before=points_before,
                        points_after=points_after,
                        reason='管理员修改'
                    )
            obj.save()
        else:
            user = USER.objects.create_user(username=obj.username, password=obj.password)
            user.set_password(obj.password)
            user.steam.personaname = obj.username
            user.steam.avatar = '/media/user/default_avatar.jpg'
            user.steam.avatarmedium = '/media/user/default_avatar.jpg'
            user.steam.avatarfull = '/media/user/default_avatar.jpg'
            user.steam.save(update_fields=['avatar', 'avatarmedium', 'avatarfull', 'personaname'])
        


@admin.register(UserStatisticsDay)
class UserStatisticsDayAdmin(ReadOnlyAdmin):
    fields = ('date', 'count')
    list_display = ('date', 'count')
    list_filter = ()  # 暂时禁用,) 暂时禁用
    list_per_page = 50

    def get_changelist(self, request, **kwargs):
        return UserStatisticsDayAddTotalChangeList


class UserStatisticsDayAddTotalChangeList(ChangeList):

    def get_results(self, request):
        super().get_results(request)
        total = UserStatisticsDay()
        setattr(total, 'date', 'total')
        fields_to_total = ['count']
        for field in fields_to_total:
            setattr(total, field, round(list(self.queryset.aggregate(Sum(field)).items())[0][1] or 0, 2))
        len(self.result_list)
        self.result_list._result_cache.insert(0, total)


@admin.register(LogEntry)
class LogEntryAdmin(admin.ModelAdmin):
    list_display = ['object_repr', 'object_id', 'action_flag', 'user', 'change_message']




